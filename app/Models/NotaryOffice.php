<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Storage;

class NotaryOffice extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'code',
        'address',
        'phone',
        'email',
        'website',
        'description',
        'image',
        'status',
        'sort_order',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Relationships
     */

    /**
     * Get notaries that belong to this office
     */
    public function notaries(): HasMany
    {
        return $this->hasMany(Notary::class);
    }

    /**
     * Scopes
     */

    /**
     * Scope a query to only include active offices.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include inactive offices.
     */
    public function scopeInactive($query)
    {
        return $query->where('status', 'inactive');
    }

    /**
     * Scope a query to order by sort_order and name.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Accessors
     */

    /**
     * Get the status badge HTML.
     */
    public function getStatusBadgeAttribute(): string
    {
        $class = $this->status === 'active' ? 'success' : 'secondary';
        $text = $this->status === 'active' ? 'Hoạt động' : 'Không hoạt động';

        return '<span class="badge bg-label-' . $class . '">' . $text . '</span>';
    }

    /**
     * Get the status badge class.
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return $this->status === 'active' ? 'bg-label-success' : 'bg-label-secondary';
    }

    /**
     * Get the status label.
     */
    public function getStatusLabelAttribute(): string
    {
        return $this->status === 'active' ? 'Hoạt động' : 'Không hoạt động';
    }

    /**
     * Get the image URL.
     */
    public function getImageUrlAttribute(): ?string
    {
        if (!$this->image) {
            return null;
        }

        return Storage::url($this->image);
    }

    /**
     * Get the formatted created date.
     */
    public function getFormattedCreatedAtAttribute(): string
    {
        return $this->created_at->format('d/m/Y H:i');
    }

    /**
     * Get the formatted updated date.
     */
    public function getFormattedUpdatedAtAttribute(): string
    {
        return $this->updated_at->format('d/m/Y H:i');
    }

    /**
     * Mutators
     */

    /**
     * Set the name attribute.
     */
    public function setNameAttribute($value): void
    {
        $this->attributes['name'] = trim($value);
    }

    /**
     * Set the code attribute.
     */
    public function setCodeAttribute($value): void
    {
        $this->attributes['code'] = strtoupper(trim($value));
    }

    /**
     * Set the email attribute.
     */
    public function setEmailAttribute($value): void
    {
        $this->attributes['email'] = $value ? strtolower(trim($value)) : null;
    }

    /**
     * Set the sort_order attribute.
     */
    public function setSortOrderAttribute($value): void
    {
        if ($value === '' || $value === null) {
            $this->attributes['sort_order'] = null;
        } else {
            $this->attributes['sort_order'] = (int) $value;
        }
    }

    /**
     * Static methods
     */

    /**
     * Get the next sort order.
     */
    public static function getNextSortOrder(): int
    {
        return static::max('sort_order') + 1;
    }

    /**
     * Get active offices for dropdown.
     */
    public static function getActiveOptions(): array
    {
        return static::active()
            ->ordered()
            ->pluck('name', 'id')
            ->toArray();
    }

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        // Set default sort order when creating
        static::creating(function ($model) {
            if ($model->sort_order === null || $model->sort_order === '' || $model->sort_order === 0) {
                $model->sort_order = static::getNextSortOrder();
            }
        });

        // Clean up files when deleting
        static::deleting(function ($model) {
            if ($model->image && Storage::exists($model->image)) {
                Storage::delete($model->image);
            }
        });
    }
}
