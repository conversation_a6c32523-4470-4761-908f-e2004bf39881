<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class Asset extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'asset_code',
        'name',
        'description',
        'land_origin_source',
        'transfer_recognition',
        'owner_info',
        'map_sheet_number',
        'address',
        'ward',
        'district',
        'province',
        'total_area',
        'residential_area',
        'agricultural_area',
        'usage_form',
        'usage_purpose',
        'usage_term',
        'asset_type_id',
        'status',
        'estimated_value',
        'acquisition_date',
        'notes',
        'documents',
        'images',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'total_area' => 'decimal:2',
        'residential_area' => 'decimal:2',
        'agricultural_area' => 'decimal:2',
        'estimated_value' => 'decimal:2',
        'acquisition_date' => 'date',
        'documents' => 'array',
        'images' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Relationships
     */

    /**
     * Get the asset type that owns the asset.
     */
    public function assetType(): BelongsTo
    {
        return $this->belongsTo(AssetType::class);
    }

    /**
     * Scopes
     */

    /**
     * Scope a query to only include active assets.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include inactive assets.
     */
    public function scopeInactive($query)
    {
        return $query->where('status', 'inactive');
    }

    /**
     * Scope a query to filter by asset type.
     */
    public function scopeByAssetType($query, $assetTypeId)
    {
        return $query->where('asset_type_id', $assetTypeId);
    }

    /**
     * Scope a query to filter by location.
     */
    public function scopeByLocation($query, $province = null, $district = null, $ward = null)
    {
        if ($province) {
            $query->where('province', 'like', "%{$province}%");
        }
        if ($district) {
            $query->where('district', 'like', "%{$district}%");
        }
        if ($ward) {
            $query->where('ward', 'like', "%{$ward}%");
        }
        return $query;
    }

    /**
     * Accessors & Mutators
     */

    /**
     * Get the full address attribute.
     */
    public function getFullAddressAttribute(): string
    {
        $parts = array_filter([
            $this->address,
            $this->ward,
            $this->district,
            $this->province
        ]);

        return implode(', ', $parts);
    }

    /**
     * Get the formatted estimated value.
     */
    public function getFormattedEstimatedValueAttribute(): string
    {
        if (!$this->estimated_value) {
            return 'Chưa định giá';
        }

        return number_format($this->estimated_value, 0, ',', '.') . ' VND';
    }

    /**
     * Get the status label.
     */
    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            'active' => 'Đang sử dụng',
            'inactive' => 'Không sử dụng',
            'sold' => 'Đã bán',
            'transferred' => 'Đã chuyển nhượng',
            default => 'Không xác định'
        };
    }

    /**
     * Get the status badge class.
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match($this->status) {
            'active' => 'bg-label-success',
            'inactive' => 'bg-label-secondary',
            'sold' => 'bg-label-warning',
            'transferred' => 'bg-label-info',
            default => 'bg-label-dark'
        };
    }

    /**
     * Helper Methods
     */

    /**
     * Generate unique asset code.
     */
    public static function generateAssetCode(): string
    {
        $prefix = 'TS';
        $year = date('Y');
        $lastAsset = static::whereYear('created_at', $year)
            ->where('asset_code', 'like', "{$prefix}{$year}%")
            ->orderBy('asset_code', 'desc')
            ->first();

        if ($lastAsset) {
            $lastNumber = (int) substr($lastAsset->asset_code, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . $year . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-generate asset code when creating
        static::creating(function ($model) {
            if (empty($model->asset_code)) {
                $model->asset_code = static::generateAssetCode();
            }
        });

        // Clean up files when deleting
        static::deleting(function ($model) {
            // Delete document files
            if ($model->documents) {
                foreach ($model->documents as $document) {
                    if (isset($document['path']) && Storage::exists($document['path'])) {
                        Storage::delete($document['path']);
                    }
                }
            }

            // Delete image files
            if ($model->images) {
                foreach ($model->images as $image) {
                    if (isset($image['path']) && Storage::exists($image['path'])) {
                        Storage::delete($image['path']);
                    }
                }
            }
        });
    }
}
