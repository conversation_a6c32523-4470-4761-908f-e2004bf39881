<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Party extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'full_name',
        'date_of_birth',
        'gender',
        'id_number',
        'id_issue_date',
        'id_issue_place',
        'permanent_address',
        'temporary_address',
        'phone_number',
        'email',
        'marital_status',
        'spouse_name',
        'spouse_date_of_birth',
        'spouse_id_number',
        'spouse_address',
        'marriage_certificate_number',
        'marriage_certificate_date',
        'marriage_certificate_place',

        'occupation',
        'workplace',
        'workplace_address',
        'role_in_case',
        'notes',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'date_of_birth' => 'date',
        'id_issue_date' => 'date',
        'spouse_date_of_birth' => 'date',
        'marriage_certificate_date' => 'date',

        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Scopes
     */

    /**
     * Scope a query to only include active parties.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include inactive parties.
     */
    public function scopeInactive($query)
    {
        return $query->where('status', 'inactive');
    }

    /**
     * Scope a query to filter by role in case.
     */
    public function scopeByRole($query, $role)
    {
        return $query->where('role_in_case', $role);
    }

    /**
     * Scope a query to filter by marital status.
     */
    public function scopeByMaritalStatus($query, $status)
    {
        return $query->where('marital_status', $status);
    }

    /**
     * Accessors & Mutators
     */

    /**
     * Get the party's age.
     */
    public function getAgeAttribute(): ?int
    {
        if (!$this->date_of_birth) {
            return null;
        }

        return Carbon::parse($this->date_of_birth)->age;
    }

    /**
     * Get the formatted date of birth.
     */
    public function getFormattedDateOfBirthAttribute(): ?string
    {
        if (!$this->date_of_birth) {
            return null;
        }

        return Carbon::parse($this->date_of_birth)->format('d/m/Y');
    }

    /**
     * Get the formatted spouse date of birth.
     */
    public function getFormattedSpouseDateOfBirthAttribute(): ?string
    {
        if (!$this->spouse_date_of_birth) {
            return null;
        }

        return Carbon::parse($this->spouse_date_of_birth)->format('d/m/Y');
    }

    /**
     * Get the formatted created at.
     */
    public function getFormattedCreatedAtAttribute(): string
    {
        return $this->created_at->format('d/m/Y H:i');
    }

    /**
     * Get the gender label in Vietnamese.
     */
    public function getGenderLabelAttribute(): string
    {
        $labels = [
            'male' => 'Nam',
            'female' => 'Nữ',
            'other' => 'Khác'
        ];

        return $labels[$this->gender] ?? 'Không xác định';
    }

    /**
     * Get the marital status label in Vietnamese.
     */
    public function getMaritalStatusLabelAttribute(): string
    {
        $labels = [
            'single' => 'Độc thân',
            'married' => 'Đã kết hôn',
            'divorced' => 'Đã ly hôn',
            'widowed' => 'Góa phụ/Góa chồng'
        ];

        return $labels[$this->marital_status] ?? 'Không xác định';
    }

    /**
     * Get the role in case label in Vietnamese.
     */
    public function getRoleInCaseLabelAttribute(): string
    {
        $labels = [
            'plaintiff' => 'Nguyên đơn',
            'defendant' => 'Bị đơn',
            'related_party' => 'Người có quyền lợi nghĩa vụ liên quan'
        ];

        return $labels[$this->role_in_case] ?? 'Không xác định';
    }

    /**
     * Get the status badge HTML.
     */
    public function getStatusBadgeAttribute(): string
    {
        $badgeClass = $this->status === 'active' ? 'bg-label-success' : 'bg-label-secondary';
        $statusText = $this->status === 'active' ? 'Hoạt động' : 'Không hoạt động';

        return '<span class="badge ' . $badgeClass . '">' . $statusText . '</span>';
    }

    /**
     * Get the role badge HTML.
     */
    public function getRoleBadgeAttribute(): string
    {
        $badgeClasses = [
            'plaintiff' => 'bg-label-primary',
            'defendant' => 'bg-label-warning',
            'related_party' => 'bg-label-info'
        ];

        $badgeClass = $badgeClasses[$this->role_in_case] ?? 'bg-label-secondary';

        return '<span class="badge ' . $badgeClass . '">' . $this->role_in_case_label . '</span>';
    }

    /**
     * Get the full address (permanent or temporary).
     */
    public function getFullAddressAttribute(): string
    {
        return $this->permanent_address ?: ($this->temporary_address ?: 'Chưa có địa chỉ');
    }

    /**
     * Check if party is married.
     */
    public function getIsMarriedAttribute(): bool
    {
        return $this->marital_status === 'married';
    }

    /**
     * Get spouse information summary.
     */
    public function getSpouseInfoAttribute(): ?string
    {
        if (!$this->is_married || !$this->spouse_name) {
            return null;
        }

        $info = $this->spouse_name;
        if ($this->spouse_date_of_birth) {
            $spouseAge = Carbon::parse($this->spouse_date_of_birth)->age;
            $info .= ' (' . $spouseAge . ' tuổi)';
        }

        return $info;
    }

    /**
     * Static methods
     */

    /**
     * Get all gender options.
     */
    public static function getGenderOptions(): array
    {
        return [
            'male' => 'Nam',
            'female' => 'Nữ',
            'other' => 'Khác'
        ];
    }

    /**
     * Get all marital status options.
     */
    public static function getMaritalStatusOptions(): array
    {
        return [
            'single' => 'Độc thân',
            'married' => 'Đã kết hôn',
            'divorced' => 'Đã ly hôn',
            'widowed' => 'Góa phụ/Góa chồng'
        ];
    }

    /**
     * Get all role in case options.
     */
    public static function getRoleInCaseOptions(): array
    {
        return [
            'plaintiff' => 'Nguyên đơn',
            'defendant' => 'Bị đơn',
            'related_party' => 'Người có quyền lợi nghĩa vụ liên quan'
        ];
    }

    /**
     * Get all status options.
     */
    public static function getStatusOptions(): array
    {
        return [
            'active' => 'Hoạt động',
            'inactive' => 'Không hoạt động'
        ];
    }
}
