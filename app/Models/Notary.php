<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class Notary extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'notary_office_id',
        'full_name',
        'position',
        'certificate_number',
        'certificate_issue_date',
        'certificate_issue_place',
        'address',
        'phone',
        'email',
        'image',
        'status',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'certificate_issue_date' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Relationships
     */

    /**
     * Get the notary office that owns the notary
     */
    public function notaryOffice(): BelongsTo
    {
        return $this->belongsTo(NotaryOffice::class);
    }

    /**
     * Scopes
     */

    /**
     * Scope a query to only include active notaries.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include inactive notaries.
     */
    public function scopeInactive($query)
    {
        return $query->where('status', 'inactive');
    }

    /**
     * Scope a query to filter by notary office.
     */
    public function scopeByOffice($query, $officeId)
    {
        return $query->where('notary_office_id', $officeId);
    }

    /**
     * Scope a query to order by full name.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('full_name');
    }

    /**
     * Accessors
     */

    /**
     * Get the status badge HTML.
     */
    public function getStatusBadgeAttribute(): string
    {
        $class = $this->status === 'active' ? 'success' : 'secondary';
        $text = $this->status === 'active' ? 'Hoạt động' : 'Không hoạt động';

        return '<span class="badge bg-label-' . $class . '">' . $text . '</span>';
    }

    /**
     * Get the status badge class.
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return $this->status === 'active' ? 'bg-label-success' : 'bg-label-secondary';
    }

    /**
     * Get the status label.
     */
    public function getStatusLabelAttribute(): string
    {
        return $this->status === 'active' ? 'Hoạt động' : 'Không hoạt động';
    }

    /**
     * Get the image URL.
     */
    public function getImageUrlAttribute(): ?string
    {
        if (!$this->image) {
            return null;
        }

        return Storage::url($this->image);
    }

    /**
     * Get the formatted certificate issue date.
     */
    public function getFormattedCertificateIssueDateAttribute(): ?string
    {
        if (!$this->certificate_issue_date) {
            return null;
        }

        return $this->certificate_issue_date->format('d/m/Y');
    }

    /**
     * Get the formatted created date.
     */
    public function getFormattedCreatedAtAttribute(): string
    {
        return $this->created_at->format('d/m/Y H:i');
    }

    /**
     * Get the formatted updated date.
     */
    public function getFormattedUpdatedAtAttribute(): string
    {
        return $this->updated_at->format('d/m/Y H:i');
    }

    /**
     * Get the notary office name.
     */
    public function getNotaryOfficeNameAttribute(): string
    {
        return $this->notaryOffice ? $this->notaryOffice->name : '-';
    }

    /**
     * Mutators
     */

    /**
     * Set the full_name attribute.
     */
    public function setFullNameAttribute($value): void
    {
        $this->attributes['full_name'] = trim($value);
    }

    /**
     * Set the email attribute.
     */
    public function setEmailAttribute($value): void
    {
        $this->attributes['email'] = $value ? strtolower(trim($value)) : null;
    }

    /**
     * Set the certificate_number attribute.
     */
    public function setCertificateNumberAttribute($value): void
    {
        $this->attributes['certificate_number'] = strtoupper(trim($value));
    }

    /**
     * Static methods
     */

    /**
     * Get active notaries for dropdown.
     */
    public static function getActiveOptions(): array
    {
        return static::active()
            ->ordered()
            ->pluck('full_name', 'id')
            ->toArray();
    }

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        // Clean up files when deleting
        static::deleting(function ($model) {
            if ($model->image && Storage::exists($model->image)) {
                Storage::delete($model->image);
            }
        });
    }
}
