<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreNotaryOfficeRequest;
use App\Http\Requests\UpdateNotaryOfficeRequest;
use App\Models\NotaryOffice;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class NotaryOfficeController extends Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->middleware('auth');
        // $this->middleware('permission:manage-notary-offices'); // Uncomment if using permissions
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Statistics for dashboard cards
        $totalCount = NotaryOffice::count();
        $activeCount = NotaryOffice::active()->count();
        $inactiveCount = NotaryOffice::inactive()->count();

        return view('admin.notary-offices.index', compact(
            'totalCount',
            'activeCount',
            'inactiveCount'
        ));
    }

    /**
     * Get data for DataTables
     */
    public function getData(Request $request)
    {
        $query = NotaryOffice::query();

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('address', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Get total count before pagination
        $totalRecords = $query->count();

        // Apply sorting
        $sortColumn = $request->get('order.0.column', 0);
        $sortDirection = $request->get('order.0.dir', 'asc');

        $columns = ['id', 'name', 'code', 'address', 'phone', 'status', 'created_at'];
        if (isset($columns[$sortColumn])) {
            $query->orderBy($columns[$sortColumn], $sortDirection);
        } else {
            $query->orderBy('sort_order')->orderBy('name');
        }

        // Apply pagination
        $start = $request->get('start', 0);
        $length = $request->get('length', 10);

        if ($length != -1) {
            $query->skip($start)->take($length);
        }

        $data = $query->get();

        // Transform data for DataTables
        $data = $data->map(function ($item) {
            return [
                'id' => $item->id,
                'name' => $item->name,
                'code' => $item->code,
                'address' => Str::limit($item->address, 50),
                'phone' => $item->phone ?: '-',
                'email' => $item->email ?: '-',
                'status_badge' => '<span class="badge ' . $item->status_badge_class . '">' . $item->status_label . '</span>',
                'created_at' => $item->created_at->format('d/m/Y H:i'),
                'actions' => view('admin.notary-offices.partials.actions', compact('item'))->render()
            ];
        });

        return response()->json([
            'draw' => intval($request->get('draw')),
            'recordsTotal' => NotaryOffice::count(),
            'recordsFiltered' => $totalRecords,
            'data' => $data
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.notary-offices.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreNotaryOfficeRequest $request)
    {
        try {
            $data = $request->validated();

            // Handle image upload
            if ($request->hasFile('image')) {
                $data['image'] = $request->file('image')->store('notary-offices', 'public');
            }

            // Set sort order if not provided
            if (!isset($data['sort_order'])) {
                $data['sort_order'] = NotaryOffice::getNextSortOrder();
            }

            $notaryOffice = NotaryOffice::create($data);

            return redirect()
                ->route('admin.notary-offices.index')
                ->with('success', 'Phòng công chứng đã được tạo thành công.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Có lỗi xảy ra: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(NotaryOffice $notaryOffice)
    {
        $notaryOffice->load('notaries');

        return view('admin.notary-offices.show', compact('notaryOffice'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(NotaryOffice $notaryOffice)
    {
        return view('admin.notary-offices.edit', compact('notaryOffice'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateNotaryOfficeRequest $request, NotaryOffice $notaryOffice)
    {
        try {
            $data = $request->validated();

            // Handle image upload
            if ($request->hasFile('image')) {
                // Delete old image if exists
                if ($notaryOffice->image && Storage::disk('public')->exists($notaryOffice->image)) {
                    Storage::disk('public')->delete($notaryOffice->image);
                }

                $data['image'] = $request->file('image')->store('notary-offices', 'public');
            }

            // Handle image removal
            if ($request->remove_image && $notaryOffice->image) {
                if (Storage::disk('public')->exists($notaryOffice->image)) {
                    Storage::disk('public')->delete($notaryOffice->image);
                }
                $data['image'] = null;
            }

            $notaryOffice->update($data);

            return redirect()
                ->route('admin.notary-offices.index')
                ->with('success', 'Phòng công chứng đã được cập nhật thành công.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Có lỗi xảy ra: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(NotaryOffice $notaryOffice)
    {
        try {
            // Check if office has notaries
            if ($notaryOffice->notaries()->count() > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không thể xóa phòng công chứng này vì đang có công chứng viên thuộc phòng.'
                ], 400);
            }

            $notaryOffice->delete();

            return response()->json([
                'success' => true,
                'message' => 'Phòng công chứng đã được xóa thành công.'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle status of the specified resource.
     */
    public function toggleStatus(NotaryOffice $notaryOffice)
    {
        try {
            $newStatus = $notaryOffice->status === 'active' ? 'inactive' : 'active';
            $notaryOffice->update(['status' => $newStatus]);

            return response()->json([
                'success' => true,
                'message' => 'Trạng thái đã được cập nhật thành công.',
                'new_status' => $newStatus,
                'status_badge' => $notaryOffice->status_badge
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get notary offices for Select2 dropdown
     */
    public function getNotaryOffices(Request $request)
    {
        $query = NotaryOffice::active()->ordered();

        if ($request->filled('q')) {
            $search = $request->q;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%");
            });
        }

        $offices = $query->limit(20)->get();

        $results = $offices->map(function ($office) {
            return [
                'id' => $office->id,
                'text' => $office->name . ' (' . $office->code . ')'
            ];
        });

        return response()->json([
            'results' => $results,
            'pagination' => ['more' => false]
        ]);
    }
}
