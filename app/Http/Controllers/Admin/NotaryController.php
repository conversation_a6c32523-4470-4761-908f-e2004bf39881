<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreNotaryRequest;
use App\Http\Requests\UpdateNotaryRequest;
use App\Models\Notary;
use App\Models\NotaryOffice;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class NotaryController extends Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->middleware('auth');
        // $this->middleware('permission:manage-notaries'); // Uncomment if using permissions
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Statistics for dashboard cards
        $totalCount = Notary::count();
        $activeCount = Notary::active()->count();
        $inactiveCount = Notary::inactive()->count();

        // Get notary offices for filter dropdown
        $notaryOffices = NotaryOffice::active()->ordered()->get();

        return view('admin.notaries.index', compact(
            'totalCount',
            'activeCount',
            'inactiveCount',
            'notaryOffices'
        ));
    }

    /**
     * Get data for DataTables
     */
    public function getData(Request $request)
    {
        $query = Notary::with('notaryOffice');

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('notary_office_id')) {
            $query->where('notary_office_id', $request->notary_office_id);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('full_name', 'like', "%{$search}%")
                  ->orWhere('certificate_number', 'like', "%{$search}%")
                  ->orWhere('position', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Get total count before pagination
        $totalRecords = $query->count();

        // Apply sorting
        $sortColumn = $request->get('order.0.column', 0);
        $sortDirection = $request->get('order.0.dir', 'asc');

        $columns = ['id', 'full_name', 'position', 'certificate_number', 'notary_office_id', 'status', 'created_at'];
        if (isset($columns[$sortColumn])) {
            if ($columns[$sortColumn] === 'notary_office_id') {
                $query->join('notary_offices', 'notaries.notary_office_id', '=', 'notary_offices.id')
                      ->orderBy('notary_offices.name', $sortDirection)
                      ->select('notaries.*');
            } else {
                $query->orderBy($columns[$sortColumn], $sortDirection);
            }
        } else {
            $query->orderBy('full_name');
        }

        // Apply pagination
        $start = $request->get('start', 0);
        $length = $request->get('length', 10);

        if ($length != -1) {
            $query->skip($start)->take($length);
        }

        $data = $query->get();

        // Transform data for DataTables
        $data = $data->map(function ($item) {
            return [
                'id' => $item->id,
                'full_name' => $item->full_name,
                'position' => $item->position ?: '-',
                'certificate_number' => $item->certificate_number,
                'notary_office_name' => $item->notary_office_name,
                'phone' => $item->phone ?: '-',
                'email' => $item->email ?: '-',
                'status_badge' => '<span class="badge ' . $item->status_badge_class . '">' . $item->status_label . '</span>',
                'created_at' => $item->created_at->format('d/m/Y H:i'),
                'actions' => view('admin.notaries.partials.actions', compact('item'))->render()
            ];
        });

        return response()->json([
            'draw' => intval($request->get('draw')),
            'recordsTotal' => Notary::count(),
            'recordsFiltered' => $totalRecords,
            'data' => $data
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $notaryOffices = NotaryOffice::active()->ordered()->get();

        return view('admin.notaries.create', compact('notaryOffices'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreNotaryRequest $request)
    {
        try {
            $data = $request->validated();

            // Handle image upload
            if ($request->hasFile('image')) {
                $data['image'] = $request->file('image')->store('notaries', 'public');
            }

            $notary = Notary::create($data);

            return redirect()
                ->route('admin.notaries.index')
                ->with('success', 'Công chứng viên đã được tạo thành công.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Có lỗi xảy ra: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Notary $notary)
    {
        $notary->load('notaryOffice');

        return view('admin.notaries.show', compact('notary'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Notary $notary)
    {
        $notaryOffices = NotaryOffice::active()->ordered()->get();

        return view('admin.notaries.edit', compact('notary', 'notaryOffices'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateNotaryRequest $request, Notary $notary)
    {
        try {
            $data = $request->validated();

            // Handle image upload
            if ($request->hasFile('image')) {
                // Delete old image if exists
                if ($notary->image && Storage::disk('public')->exists($notary->image)) {
                    Storage::disk('public')->delete($notary->image);
                }

                $data['image'] = $request->file('image')->store('notaries', 'public');
            }

            // Handle image removal
            if ($request->remove_image && $notary->image) {
                if (Storage::disk('public')->exists($notary->image)) {
                    Storage::disk('public')->delete($notary->image);
                }
                $data['image'] = null;
            }

            $notary->update($data);

            return redirect()
                ->route('admin.notaries.index')
                ->with('success', 'Công chứng viên đã được cập nhật thành công.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Có lỗi xảy ra: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Notary $notary)
    {
        try {
            $notary->delete();

            return response()->json([
                'success' => true,
                'message' => 'Công chứng viên đã được xóa thành công.'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle status of the specified resource.
     */
    public function toggleStatus(Notary $notary)
    {
        try {
            $newStatus = $notary->status === 'active' ? 'inactive' : 'active';
            $notary->update(['status' => $newStatus]);

            return response()->json([
                'success' => true,
                'message' => 'Trạng thái đã được cập nhật thành công.',
                'new_status' => $newStatus,
                'status_badge' => $notary->status_badge
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get notaries for Select2 dropdown
     */
    public function getNotaries(Request $request)
    {
        $query = Notary::active()->ordered();

        if ($request->filled('q')) {
            $search = $request->q;
            $query->where(function ($q) use ($search) {
                $q->where('full_name', 'like', "%{$search}%")
                  ->orWhere('certificate_number', 'like', "%{$search}%");
            });
        }

        if ($request->filled('notary_office_id')) {
            $query->where('notary_office_id', $request->notary_office_id);
        }

        $notaries = $query->limit(20)->get();

        $results = $notaries->map(function ($notary) {
            return [
                'id' => $notary->id,
                'text' => $notary->full_name . ' (' . $notary->certificate_number . ')'
            ];
        });

        return response()->json([
            'results' => $results,
            'pagination' => ['more' => false]
        ]);
    }
}
