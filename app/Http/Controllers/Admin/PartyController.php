<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\StorePartyRequest;
use App\Http\Requests\UpdatePartyRequest;
use App\Models\Party;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;

class PartyController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        // Statistics for dashboard cards
        $totalCount = Party::count();
        $activeCount = Party::active()->count();
        $inactiveCount = Party::inactive()->count();
        $plaintiffCount = Party::byRole('plaintiff')->count();
        $defendantCount = Party::byRole('defendant')->count();
        $relatedPartyCount = Party::byRole('related_party')->count();

        return view('admin.parties.index', compact(
            'totalCount',
            'activeCount',
            'inactiveCount',
            'plaintiffCount',
            'defendantCount',
            'relatedPartyCount'
        ));
    }

    /**
     * Get data for DataTables AJAX request.
     */
    public function getData(Request $request): JsonResponse
    {
        $query = Party::query();

        // Apply filters
        if ($request->filled('status_filter')) {
            $query->where('status', $request->status_filter);
        }

        if ($request->filled('role_filter')) {
            $query->where('role_in_case', $request->role_filter);
        }

        if ($request->filled('marital_status_filter')) {
            $query->where('marital_status', $request->marital_status_filter);
        }

        // Search functionality
        if ($request->filled('search.value')) {
            $searchValue = $request->input('search.value');
            $query->where(function ($q) use ($searchValue) {
                $q->where('full_name', 'like', "%{$searchValue}%")
                  ->orWhere('id_number', 'like', "%{$searchValue}%")
                  ->orWhere('phone_number', 'like', "%{$searchValue}%")
                  ->orWhere('email', 'like', "%{$searchValue}%")
                  ->orWhere('occupation', 'like', "%{$searchValue}%")
                  ->orWhere('workplace', 'like', "%{$searchValue}%");
            });
        }

        // Sorting
        if ($request->filled('order')) {
            $orderColumn = $request->input('order.0.column');
            $orderDirection = $request->input('order.0.dir');

            $columns = ['id', 'full_name', 'id_number', 'phone_number', 'role_in_case', 'marital_status', 'status', 'created_at'];

            if (isset($columns[$orderColumn])) {
                $query->orderBy($columns[$orderColumn], $orderDirection);
            }
        } else {
            $query->orderBy('created_at', 'desc');
        }

        // Pagination
        $recordsTotal = Party::count();
        $recordsFiltered = $query->count();

        $start = $request->start ?? 0;
        $length = $request->length ?? 10;

        $data = $query->skip($start)->take($length)->get();

        // Transform data for DataTables
        $data = $data->map(function ($item) {
            return [
                'id' => $item->id,
                'full_name' => $item->full_name,
                'id_number' => $item->id_number ?: '-',
                'phone_number' => $item->phone_number ?: '-',
                'email' => $item->email ?: '-',
                'age' => $item->age ? $item->age . ' tuổi' : '-',
                'gender_label' => $item->gender_label,
                'role_badge' => $item->role_badge,
                'marital_status_label' => $item->marital_status_label,
                'occupation' => $item->occupation ?: '-',
                'status_badge' => $item->status_badge,
                'created_at' => $item->formatted_created_at,
                'actions' => view('admin.parties.partials.actions', compact('item'))->render()
            ];
        });

        return response()->json([
            'draw' => intval($request->draw),
            'recordsTotal' => $recordsTotal,
            'recordsFiltered' => $recordsFiltered,
            'data' => $data
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.parties.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StorePartyRequest $request)
    {
        $data = $request->validated();

        $party = Party::create($data);

        return redirect()
            ->route('admin.parties.index')
            ->with('success', 'Đương sự đã được tạo thành công.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Party $party)
    {
        return view('admin.parties.show', compact('party'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Party $party)
    {
        return view('admin.parties.edit', compact('party'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdatePartyRequest $request, Party $party)
    {
        $data = $request->validated();

        $party->update($data);

        return redirect()
            ->route('admin.parties.index')
            ->with('success', 'Thông tin đương sự đã được cập nhật thành công.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Party $party): JsonResponse
    {
        try {
            $party->delete();

            return response()->json([
                'success' => true,
                'message' => 'Đương sự đã được xóa thành công.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi xóa đương sự: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle status of the specified resource.
     */
    public function toggleStatus(Party $party): JsonResponse
    {
        try {
            $newStatus = $party->status === 'active' ? 'inactive' : 'active';
            $party->update(['status' => $newStatus]);

            $statusText = $newStatus === 'active' ? 'kích hoạt' : 'vô hiệu hóa';

            return response()->json([
                'success' => true,
                'message' => "Đã {$statusText} đương sự thành công.",
                'new_status' => $newStatus,
                'status_badge' => $party->status_badge
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi thay đổi trạng thái: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get party details for quick view modal.
     */
    public function quickView(Party $party): JsonResponse
    {
        try {
            $partyData = [
                'id' => $party->id,
                'full_name' => $party->full_name,
                'date_of_birth' => $party->formatted_date_of_birth,
                'age' => $party->age ? $party->age . ' tuổi' : null,
                'gender_label' => $party->gender_label,
                'id_number' => $party->id_number,
                'id_issue_date' => $party->id_issue_date ? $party->id_issue_date->format('d/m/Y') : null,
                'id_issue_place' => $party->id_issue_place,
                'permanent_address' => $party->permanent_address,
                'temporary_address' => $party->temporary_address,
                'phone_number' => $party->phone_number,
                'email' => $party->email,
                'marital_status_label' => $party->marital_status_label,
                'spouse_info' => $party->spouse_info,
                'occupation' => $party->occupation,
                'workplace' => $party->workplace,
                'workplace_address' => $party->workplace_address,
                'role_in_case_label' => $party->role_in_case_label,
                'notes' => $party->notes,
                'status_badge' => $party->status_badge,
                'created_at' => $party->formatted_created_at,
            ];

            return response()->json([
                'success' => true,
                'data' => $partyData
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tải thông tin đương sự: ' . $e->getMessage()
            ], 500);
        }
    }
}
