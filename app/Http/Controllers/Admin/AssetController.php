<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Asset;
use App\Models\AssetType;
use App\Http\Requests\StoreAssetRequest;
use App\Http\Requests\UpdateAssetRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class AssetController extends Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->middleware('auth');
        // $this->middleware('permission:manage-assets'); // Uncomment if using permissions
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Statistics for dashboard cards
        $totalCount = Asset::count();
        $activeCount = Asset::active()->count();
        $inactiveCount = Asset::where('status', 'inactive')->count();
        $soldCount = Asset::where('status', 'sold')->count();

        // Get asset types for filter dropdown
        $assetTypes = AssetType::active()->orderBy('name')->get();

        return view('admin.assets.index', compact(
            'totalCount',
            'activeCount',
            'inactiveCount',
            'soldCount',
            'assetTypes'
        ));
    }

    /**
     * Get data for DataTables
     */
    public function getData(Request $request)
    {
        $query = Asset::with('assetType');

        // Handle custom filters
        if ($request->has('status_filter') && !empty($request->status_filter)) {
            $query->where('status', $request->status_filter);
        }

        if ($request->has('asset_type_filter') && !empty($request->asset_type_filter)) {
            $query->where('asset_type_id', $request->asset_type_filter);
        }

        if ($request->has('province_filter') && !empty($request->province_filter)) {
            $query->where('province', 'like', "%{$request->province_filter}%");
        }

        // Handle search
        if ($request->has('search') && !empty($request->search['value'])) {
            $searchValue = $request->search['value'];
            $query->where(function ($q) use ($searchValue) {
                $q->where('asset_code', 'like', "%{$searchValue}%")
                  ->orWhere('name', 'like', "%{$searchValue}%")
                  ->orWhere('address', 'like', "%{$searchValue}%")
                  ->orWhere('owner_info', 'like', "%{$searchValue}%")
                  ->orWhere('map_sheet_number', 'like', "%{$searchValue}%");
            });
        }

        // Handle ordering
        if ($request->has('order')) {
            $orderColumn = $request->columns[$request->order[0]['column']]['data'];
            $orderDirection = $request->order[0]['dir'];

            // Handle special ordering cases
            if ($orderColumn === 'asset_type_name') {
                $query->join('asset_types', 'assets.asset_type_id', '=', 'asset_types.id')
                      ->orderBy('asset_types.name', $orderDirection)
                      ->select('assets.*');
            } else {
                $query->orderBy($orderColumn, $orderDirection);
            }
        } else {
            $query->orderBy('created_at', 'desc');
        }

        // Pagination
        $recordsTotal = Asset::count();
        $recordsFiltered = $query->count();

        $start = $request->start ?? 0;
        $length = $request->length ?? 10;

        $data = $query->skip($start)->take($length)->get();

        // Transform data for DataTables
        $data = $data->map(function ($item) {
            return [
                'id' => $item->id,
                'asset_code' => $item->asset_code,
                'name' => $item->name,
                'asset_type_name' => $item->assetType ? $item->assetType->name : '-',
                'full_address' => Str::limit($item->full_address, 50),
                'total_area' => $item->total_area ? number_format($item->total_area, 2) . ' m²' : '-',
                'estimated_value' => $item->formatted_estimated_value,
                'status_badge' => '<span class="badge ' . $item->status_badge_class . '">' . $item->status_label . '</span>',
                'acquisition_date' => $item->acquisition_date ? $item->acquisition_date->format('d/m/Y') : '-',
                'created_at' => $item->created_at->format('d/m/Y H:i'),
                'actions' => view('admin.assets.partials.actions', compact('item'))->render()
            ];
        });

        return response()->json([
            'draw' => $request->draw,
            'recordsTotal' => $recordsTotal,
            'recordsFiltered' => $recordsFiltered,
            'data' => $data
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $assetTypes = AssetType::active()->orderBy('name')->get();
        $asset = null; // For form compatibility

        return view('admin.assets.create', compact('assetTypes', 'asset'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreAssetRequest $request)
    {
        try {
            $validatedData = $request->validated();

            // Handle file uploads
            if ($request->hasFile('document_files')) {
                $documents = [];
                foreach ($request->file('document_files') as $file) {
                    $path = $file->store('assets/documents', 'public');
                    $documents[] = [
                        'name' => $file->getClientOriginalName(),
                        'path' => $path,
                        'size' => $file->getSize(),
                        'type' => $file->getClientMimeType()
                    ];
                }
                $validatedData['documents'] = $documents;
            }

            if ($request->hasFile('image_files')) {
                $images = [];
                foreach ($request->file('image_files') as $file) {
                    $path = $file->store('assets/images', 'public');
                    $images[] = [
                        'name' => $file->getClientOriginalName(),
                        'path' => $path,
                        'size' => $file->getSize(),
                        'type' => $file->getClientMimeType()
                    ];
                }
                $validatedData['images'] = $images;
            }

            $asset = Asset::create($validatedData);

            return redirect()->route('admin.assets.index')
                ->with('success', 'Tài sản đã được tạo thành công!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Có lỗi xảy ra: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Asset $asset)
    {
        $asset->load('assetType');

        return view('admin.assets.show', compact('asset'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Asset $asset)
    {
        $assetTypes = AssetType::active()->orderBy('name')->get();

        return view('admin.assets.edit', compact('asset', 'assetTypes'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateAssetRequest $request, Asset $asset)
    {
        try {
            $validatedData = $request->validated();

            // Handle file uploads
            if ($request->hasFile('document_files')) {
                // Delete old documents if replacing
                if ($asset->documents) {
                    foreach ($asset->documents as $document) {
                        if (isset($document['path']) && Storage::disk('public')->exists($document['path'])) {
                            Storage::disk('public')->delete($document['path']);
                        }
                    }
                }

                $documents = [];
                foreach ($request->file('document_files') as $file) {
                    $path = $file->store('assets/documents', 'public');
                    $documents[] = [
                        'name' => $file->getClientOriginalName(),
                        'path' => $path,
                        'size' => $file->getSize(),
                        'type' => $file->getClientMimeType()
                    ];
                }
                $validatedData['documents'] = $documents;
            }

            if ($request->hasFile('image_files')) {
                // Delete old images if replacing
                if ($asset->images) {
                    foreach ($asset->images as $image) {
                        if (isset($image['path']) && Storage::disk('public')->exists($image['path'])) {
                            Storage::disk('public')->delete($image['path']);
                        }
                    }
                }

                $images = [];
                foreach ($request->file('image_files') as $file) {
                    $path = $file->store('assets/images', 'public');
                    $images[] = [
                        'name' => $file->getClientOriginalName(),
                        'path' => $path,
                        'size' => $file->getSize(),
                        'type' => $file->getClientMimeType()
                    ];
                }
                $validatedData['images'] = $images;
            }

            $asset->update($validatedData);

            return redirect()->route('admin.assets.index')
                ->with('success', 'Tài sản đã được cập nhật thành công!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Có lỗi xảy ra: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Asset $asset)
    {
        try {
            $asset->delete();

            return redirect()->route('admin.assets.index')
                ->with('success', 'Tài sản đã được xóa thành công!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Có lỗi xảy ra: ' . $e->getMessage());
        }
    }

    /**
     * Bulk delete assets
     */
    public function bulkDelete(Request $request)
    {
        try {
            $ids = $request->input('ids', []);

            if (empty($ids)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không có tài sản nào được chọn!'
                ], 400);
            }

            Asset::whereIn('id', $ids)->delete();

            return response()->json([
                'success' => true,
                'message' => 'Đã xóa ' . count($ids) . ' tài sản thành công!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export assets to Excel
     */
    public function export(Request $request)
    {
        // This would require Laravel Excel package
        // For now, return a simple CSV export

        $assets = Asset::with('assetType')->get();

        $filename = 'assets_export_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($assets) {
            $file = fopen('php://output', 'w');

            // Add BOM for UTF-8
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

            // Headers
            fputcsv($file, [
                'Mã tài sản',
                'Tên tài sản',
                'Loại tài sản',
                'Địa chỉ đầy đủ',
                'Tổng diện tích (m²)',
                'Giá trị ước tính',
                'Trạng thái',
                'Ngày tiếp nhận',
                'Ngày tạo'
            ]);

            // Data
            foreach ($assets as $asset) {
                fputcsv($file, [
                    $asset->asset_code,
                    $asset->name,
                    $asset->assetType ? $asset->assetType->name : '',
                    $asset->full_address,
                    $asset->total_area,
                    $asset->estimated_value,
                    $asset->status_label,
                    $asset->acquisition_date ? $asset->acquisition_date->format('d/m/Y') : '',
                    $asset->created_at->format('d/m/Y H:i')
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
