<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateAssetRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Or implement permission check: auth()->user()->can('update-assets')
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $assetId = $this->route('asset')->id ?? null;

        return [
            // Basic asset information
            'asset_code' => 'nullable|string|max:50|unique:assets,asset_code,' . $assetId,
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:2000',

            // Land use rights information
            'land_origin_source' => 'nullable|string|max:1000',
            'transfer_recognition' => 'nullable|string|max:1000',
            'owner_info' => 'nullable|string|max:1000',
            'map_sheet_number' => 'nullable|string|max:100',

            // Address information
            'address' => 'nullable|string|max:500',
            'ward' => 'nullable|string|max:255',
            'district' => 'nullable|string|max:255',
            'province' => 'nullable|string|max:255',

            // Area and usage information
            'total_area' => 'nullable|numeric|min:0|max:999999.99',
            'residential_area' => 'nullable|numeric|min:0|max:999999.99',
            'agricultural_area' => 'nullable|numeric|min:0|max:999999.99',
            'usage_form' => 'nullable|string|max:255',
            'usage_purpose' => 'nullable|string|max:1000',
            'usage_term' => 'nullable|string|max:1000',

            // Relationships and metadata
            'asset_type_id' => 'nullable|exists:asset_types,id',
            'status' => 'required|in:active,inactive,sold,transferred',
            'estimated_value' => 'nullable|numeric|min:0|max:999999999999.99',
            'acquisition_date' => 'nullable|date|before_or_equal:today',
            'notes' => 'nullable|string|max:2000',

            // File uploads
            'document_files.*' => 'nullable|file|mimes:pdf,doc,docx,xls,xlsx,jpg,jpeg,png|max:10240', // 10MB
            'image_files.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            // Basic asset information
            'asset_code.string' => 'Mã tài sản phải là chuỗi ký tự.',
            'asset_code.max' => 'Mã tài sản không được vượt quá 50 ký tự.',
            'asset_code.unique' => 'Mã tài sản này đã tồn tại.',

            'name.required' => 'Tên tài sản là bắt buộc.',
            'name.string' => 'Tên tài sản phải là chuỗi ký tự.',
            'name.max' => 'Tên tài sản không được vượt quá 255 ký tự.',

            'description.string' => 'Mô tả phải là chuỗi ký tự.',
            'description.max' => 'Mô tả không được vượt quá 2000 ký tự.',

            // Land use rights information
            'land_origin_source.string' => 'Nguồn gốc sử dụng đất phải là chuỗi ký tự.',
            'land_origin_source.max' => 'Nguồn gốc sử dụng đất không được vượt quá 1000 ký tự.',

            'transfer_recognition.string' => 'Thông tin chuyển nhượng phải là chuỗi ký tự.',
            'transfer_recognition.max' => 'Thông tin chuyển nhượng không được vượt quá 1000 ký tự.',

            'owner_info.string' => 'Thông tin gia chủ phải là chuỗi ký tự.',
            'owner_info.max' => 'Thông tin gia chủ không được vượt quá 1000 ký tự.',

            'map_sheet_number.string' => 'Tờ bản đồ số phải là chuỗi ký tự.',
            'map_sheet_number.max' => 'Tờ bản đồ số không được vượt quá 100 ký tự.',

            // Address information
            'address.string' => 'Địa chỉ phải là chuỗi ký tự.',
            'address.max' => 'Địa chỉ không được vượt quá 500 ký tự.',

            'ward.string' => 'Xã/Phường phải là chuỗi ký tự.',
            'ward.max' => 'Xã/Phường không được vượt quá 255 ký tự.',

            'district.string' => 'Quận/Huyện phải là chuỗi ký tự.',
            'district.max' => 'Quận/Huyện không được vượt quá 255 ký tự.',

            'province.string' => 'Tỉnh/Thành phố phải là chuỗi ký tự.',
            'province.max' => 'Tỉnh/Thành phố không được vượt quá 255 ký tự.',

            // Area and usage information
            'total_area.numeric' => 'Tổng diện tích phải là số.',
            'total_area.min' => 'Tổng diện tích phải lớn hơn hoặc bằng 0.',
            'total_area.max' => 'Tổng diện tích quá lớn.',

            'residential_area.numeric' => 'Diện tích đất ở phải là số.',
            'residential_area.min' => 'Diện tích đất ở phải lớn hơn hoặc bằng 0.',
            'residential_area.max' => 'Diện tích đất ở quá lớn.',

            'agricultural_area.numeric' => 'Diện tích đất nông nghiệp phải là số.',
            'agricultural_area.min' => 'Diện tích đất nông nghiệp phải lớn hơn hoặc bằng 0.',
            'agricultural_area.max' => 'Diện tích đất nông nghiệp quá lớn.',

            'usage_form.string' => 'Hình thức sử dụng phải là chuỗi ký tự.',
            'usage_form.max' => 'Hình thức sử dụng không được vượt quá 255 ký tự.',

            'usage_purpose.string' => 'Mục đích sử dụng phải là chuỗi ký tự.',
            'usage_purpose.max' => 'Mục đích sử dụng không được vượt quá 1000 ký tự.',

            'usage_term.string' => 'Thời hạn sử dụng phải là chuỗi ký tự.',
            'usage_term.max' => 'Thời hạn sử dụng không được vượt quá 1000 ký tự.',

            // Relationships and metadata
            'asset_type_id.exists' => 'Loại tài sản được chọn không tồn tại.',

            'status.required' => 'Trạng thái là bắt buộc.',
            'status.in' => 'Trạng thái không hợp lệ.',

            'estimated_value.numeric' => 'Giá trị ước tính phải là số.',
            'estimated_value.min' => 'Giá trị ước tính phải lớn hơn hoặc bằng 0.',
            'estimated_value.max' => 'Giá trị ước tính quá lớn.',

            'acquisition_date.date' => 'Ngày tiếp nhận phải là ngày hợp lệ.',
            'acquisition_date.before_or_equal' => 'Ngày tiếp nhận không được là ngày tương lai.',

            'notes.string' => 'Ghi chú phải là chuỗi ký tự.',
            'notes.max' => 'Ghi chú không được vượt quá 2000 ký tự.',

            // File uploads
            'document_files.*.file' => 'Tài liệu phải là file hợp lệ.',
            'document_files.*.mimes' => 'Tài liệu phải có định dạng: pdf, doc, docx, xls, xlsx, jpg, jpeg, png.',
            'document_files.*.max' => 'Tài liệu không được vượt quá 10MB.',

            'image_files.*.image' => 'Hình ảnh phải là file ảnh hợp lệ.',
            'image_files.*.mimes' => 'Hình ảnh phải có định dạng: jpeg, png, jpg, gif.',
            'image_files.*.max' => 'Hình ảnh không được vượt quá 5MB.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'asset_code' => 'mã tài sản',
            'name' => 'tên tài sản',
            'description' => 'mô tả',
            'land_origin_source' => 'nguồn gốc sử dụng đất',
            'transfer_recognition' => 'thông tin chuyển nhượng',
            'owner_info' => 'thông tin gia chủ',
            'map_sheet_number' => 'tờ bản đồ số',
            'address' => 'địa chỉ',
            'ward' => 'xã/phường',
            'district' => 'quận/huyện',
            'province' => 'tỉnh/thành phố',
            'total_area' => 'tổng diện tích',
            'residential_area' => 'diện tích đất ở',
            'agricultural_area' => 'diện tích đất nông nghiệp',
            'usage_form' => 'hình thức sử dụng',
            'usage_purpose' => 'mục đích sử dụng',
            'usage_term' => 'thời hạn sử dụng',
            'asset_type_id' => 'loại tài sản',
            'status' => 'trạng thái',
            'estimated_value' => 'giá trị ước tính',
            'acquisition_date' => 'ngày tiếp nhận',
            'notes' => 'ghi chú',
        ];
    }
}
