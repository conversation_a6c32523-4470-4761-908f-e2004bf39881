<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StorePartyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Or implement permission check: auth()->user()->can('create-parties')
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // Thông tin cá nhân cơ bản
            'full_name' => 'required|string|max:255',
            'date_of_birth' => 'nullable|date|before:today',
            'gender' => 'nullable|in:male,female,other',
            'id_number' => 'nullable|string|max:20|unique:parties,id_number',
            'id_issue_date' => 'nullable|date|before_or_equal:today',
            'id_issue_place' => 'nullable|string|max:255',

            // <PERSON><PERSON><PERSON> chỉ liên hệ
            'permanent_address' => 'nullable|string|max:1000',
            'temporary_address' => 'nullable|string|max:1000',
            'phone_number' => 'nullable|string|max:20|regex:/^[0-9+\-\s()]+$/',
            'email' => 'nullable|email|max:255',

            // Tình trạng hôn nhân
            'marital_status' => 'required|in:single,married,divorced,widowed',
            'spouse_name' => 'nullable|string|max:255|required_if:marital_status,married',
            'spouse_date_of_birth' => 'nullable|date|before:today|required_if:marital_status,married',
            'spouse_id_number' => 'nullable|string|max:20',
            'spouse_address' => 'nullable|string|max:1000',

            // Thông tin giấy chứng nhận kết hôn
            'marriage_certificate_number' => 'nullable|string|max:50|required_if:marital_status,married',
            'marriage_certificate_date' => 'nullable|date|before_or_equal:today|required_if:marital_status,married',
            'marriage_certificate_place' => 'nullable|string|max:255|required_if:marital_status,married',


            // Nghề nghiệp và nơi làm việc
            'occupation' => 'nullable|string|max:255',
            'workplace' => 'nullable|string|max:255',
            'workplace_address' => 'nullable|string|max:1000',

            // Vai trò trong vụ việc
            'role_in_case' => 'required|in:plaintiff,defendant,related_party',

            // Ghi chú và trạng thái
            'notes' => 'nullable|string|max:2000',
            'status' => 'required|in:active,inactive',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            // Thông tin cá nhân
            'full_name.required' => 'Họ và tên là bắt buộc.',
            'full_name.string' => 'Họ và tên phải là chuỗi ký tự.',
            'full_name.max' => 'Họ và tên không được vượt quá 255 ký tự.',

            'date_of_birth.date' => 'Ngày sinh phải là ngày hợp lệ.',
            'date_of_birth.before' => 'Ngày sinh phải trước ngày hôm nay.',

            'gender.in' => 'Giới tính phải là Nam, Nữ hoặc Khác.',

            'id_number.string' => 'Số CCCD/CMND phải là chuỗi ký tự.',
            'id_number.max' => 'Số CCCD/CMND không được vượt quá 20 ký tự.',
            'id_number.unique' => 'Số CCCD/CMND này đã tồn tại trong hệ thống.',

            'id_issue_date.date' => 'Ngày cấp CCCD/CMND phải là ngày hợp lệ.',
            'id_issue_date.before_or_equal' => 'Ngày cấp CCCD/CMND không được sau ngày hôm nay.',

            'id_issue_place.string' => 'Nơi cấp CCCD/CMND phải là chuỗi ký tự.',
            'id_issue_place.max' => 'Nơi cấp CCCD/CMND không được vượt quá 255 ký tự.',

            // Địa chỉ liên hệ
            'permanent_address.string' => 'Địa chỉ thường trú phải là chuỗi ký tự.',
            'permanent_address.max' => 'Địa chỉ thường trú không được vượt quá 1000 ký tự.',

            'temporary_address.string' => 'Địa chỉ tạm trú phải là chuỗi ký tự.',
            'temporary_address.max' => 'Địa chỉ tạm trú không được vượt quá 1000 ký tự.',

            'phone_number.string' => 'Số điện thoại phải là chuỗi ký tự.',
            'phone_number.max' => 'Số điện thoại không được vượt quá 20 ký tự.',
            'phone_number.regex' => 'Số điện thoại không đúng định dạng.',

            'email.email' => 'Email không đúng định dạng.',
            'email.max' => 'Email không được vượt quá 255 ký tự.',

            // Tình trạng hôn nhân
            'marital_status.required' => 'Tình trạng hôn nhân là bắt buộc.',
            'marital_status.in' => 'Tình trạng hôn nhân không hợp lệ.',

            'spouse_name.string' => 'Tên vợ/chồng phải là chuỗi ký tự.',
            'spouse_name.max' => 'Tên vợ/chồng không được vượt quá 255 ký tự.',
            'spouse_name.required_if' => 'Tên vợ/chồng là bắt buộc khi đã kết hôn.',

            'spouse_date_of_birth.date' => 'Ngày sinh vợ/chồng phải là ngày hợp lệ.',
            'spouse_date_of_birth.before' => 'Ngày sinh vợ/chồng phải trước ngày hôm nay.',
            'spouse_date_of_birth.required_if' => 'Ngày sinh vợ/chồng là bắt buộc khi đã kết hôn.',

            'spouse_id_number.string' => 'Số CCCD/CMND vợ/chồng phải là chuỗi ký tự.',
            'spouse_id_number.max' => 'Số CCCD/CMND vợ/chồng không được vượt quá 20 ký tự.',

            'spouse_address.string' => 'Địa chỉ vợ/chồng phải là chuỗi ký tự.',
            'spouse_address.max' => 'Địa chỉ vợ/chồng không được vượt quá 1000 ký tự.',

            // Giấy chứng nhận kết hôn
            'marriage_certificate_number.string' => 'Số giấy chứng nhận kết hôn phải là chuỗi ký tự.',
            'marriage_certificate_number.max' => 'Số giấy chứng nhận kết hôn không được vượt quá 50 ký tự.',
            'marriage_certificate_number.required_if' => 'Số giấy chứng nhận kết hôn là bắt buộc khi đã kết hôn.',

            'marriage_certificate_date.date' => 'Ngày cấp giấy chứng nhận kết hôn phải là ngày hợp lệ.',
            'marriage_certificate_date.before_or_equal' => 'Ngày cấp giấy chứng nhận kết hôn không được sau ngày hôm nay.',
            'marriage_certificate_date.required_if' => 'Ngày cấp giấy chứng nhận kết hôn là bắt buộc khi đã kết hôn.',

            'marriage_certificate_place.string' => 'Nơi cấp giấy chứng nhận kết hôn phải là chuỗi ký tự.',
            'marriage_certificate_place.max' => 'Nơi cấp giấy chứng nhận kết hôn không được vượt quá 255 ký tự.',
            'marriage_certificate_place.required_if' => 'Nơi cấp giấy chứng nhận kết hôn là bắt buộc khi đã kết hôn.',



            // Nghề nghiệp
            'occupation.string' => 'Nghề nghiệp phải là chuỗi ký tự.',
            'occupation.max' => 'Nghề nghiệp không được vượt quá 255 ký tự.',

            'workplace.string' => 'Nơi làm việc phải là chuỗi ký tự.',
            'workplace.max' => 'Nơi làm việc không được vượt quá 255 ký tự.',

            'workplace_address.string' => 'Địa chỉ nơi làm việc phải là chuỗi ký tự.',
            'workplace_address.max' => 'Địa chỉ nơi làm việc không được vượt quá 1000 ký tự.',

            // Vai trò trong vụ việc
            'role_in_case.required' => 'Vai trò trong vụ việc là bắt buộc.',
            'role_in_case.in' => 'Vai trò trong vụ việc không hợp lệ.',

            // Ghi chú và trạng thái
            'notes.string' => 'Ghi chú phải là chuỗi ký tự.',
            'notes.max' => 'Ghi chú không được vượt quá 2000 ký tự.',

            'status.required' => 'Trạng thái là bắt buộc.',
            'status.in' => 'Trạng thái phải là Hoạt động hoặc Không hoạt động.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'full_name' => 'họ và tên',
            'date_of_birth' => 'ngày sinh',
            'gender' => 'giới tính',
            'id_number' => 'số CCCD/CMND',
            'id_issue_date' => 'ngày cấp CCCD/CMND',
            'id_issue_place' => 'nơi cấp CCCD/CMND',
            'permanent_address' => 'địa chỉ thường trú',
            'temporary_address' => 'địa chỉ tạm trú',
            'phone_number' => 'số điện thoại',
            'email' => 'email',
            'marital_status' => 'tình trạng hôn nhân',
            'spouse_name' => 'tên vợ/chồng',
            'spouse_date_of_birth' => 'ngày sinh vợ/chồng',
            'spouse_id_number' => 'số CCCD/CMND vợ/chồng',
            'spouse_address' => 'địa chỉ vợ/chồng',
            'marriage_certificate_number' => 'số giấy chứng nhận kết hôn',
            'marriage_certificate_date' => 'ngày cấp giấy chứng nhận kết hôn',
            'marriage_certificate_place' => 'nơi cấp giấy chứng nhận kết hôn',

            'occupation' => 'nghề nghiệp',
            'workplace' => 'nơi làm việc',
            'workplace_address' => 'địa chỉ nơi làm việc',
            'role_in_case' => 'vai trò trong vụ việc',
            'notes' => 'ghi chú',
            'status' => 'trạng thái',
        ];
    }
}
