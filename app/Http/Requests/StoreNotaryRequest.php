<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreNotaryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'notary_office_id' => 'nullable|exists:notary_offices,id',
            'full_name' => 'required|string|max:255',
            'position' => 'nullable|string|max:255',
            'certificate_number' => 'required|string|max:100|unique:notaries,certificate_number',
            'certificate_issue_date' => 'nullable|date',
            'certificate_issue_place' => 'nullable|string|max:255',
            'address' => 'nullable|string',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'status' => 'required|in:active,inactive',
            'notes' => 'nullable|string',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'notary_office_id' => 'phòng công chứng',
            'full_name' => 'họ và tên',
            'position' => 'chức vụ',
            'certificate_number' => 'số chứng chỉ',
            'certificate_issue_date' => 'ngày cấp chứng chỉ',
            'certificate_issue_place' => 'nơi cấp chứng chỉ',
            'address' => 'địa chỉ',
            'phone' => 'số điện thoại',
            'email' => 'email',
            'image' => 'ảnh đại diện',
            'status' => 'trạng thái',
            'notes' => 'ghi chú',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'full_name.required' => 'Vui lòng nhập họ và tên.',
            'full_name.max' => 'Họ và tên không được vượt quá 255 ký tự.',
            'certificate_number.required' => 'Vui lòng nhập số chứng chỉ.',
            'certificate_number.unique' => 'Số chứng chỉ này đã được sử dụng.',
            'certificate_issue_date.date' => 'Ngày cấp chứng chỉ không đúng định dạng.',
            'email.email' => 'Email không đúng định dạng.',
            'image.image' => 'File phải là ảnh.',
            'image.mimes' => 'Ảnh phải có định dạng: jpeg, png, jpg, gif.',
            'image.max' => 'Kích thước ảnh không được vượt quá 2MB.',
            'status.required' => 'Vui lòng chọn trạng thái.',
            'status.in' => 'Trạng thái không hợp lệ.',
            'notary_office_id.exists' => 'Phòng công chứng không tồn tại.',
        ];
    }
}
