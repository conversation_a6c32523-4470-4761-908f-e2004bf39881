<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateNotaryOfficeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'code' => [
                'required',
                'string',
                'max:50',
                Rule::unique('notary_offices', 'code')->ignore($this->notary_office)
            ],
            'address' => 'required|string',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'website' => 'nullable|url|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'status' => 'required|in:active,inactive',
            'sort_order' => 'nullable|integer|min:0',
            'remove_image' => 'nullable|boolean',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'name' => 'tên phòng công chứng',
            'code' => 'mã số',
            'address' => 'địa chỉ',
            'phone' => 'số điện thoại',
            'email' => 'email',
            'website' => 'website',
            'description' => 'mô tả',
            'image' => 'ảnh đại diện',
            'status' => 'trạng thái',
            'sort_order' => 'thứ tự sắp xếp',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Vui lòng nhập tên phòng công chứng.',
            'name.max' => 'Tên phòng công chứng không được vượt quá 255 ký tự.',
            'code.required' => 'Vui lòng nhập mã số phòng công chứng.',
            'code.unique' => 'Mã số này đã được sử dụng.',
            'address.required' => 'Vui lòng nhập địa chỉ.',
            'email.email' => 'Email không đúng định dạng.',
            'website.url' => 'Website không đúng định dạng.',
            'image.image' => 'File phải là ảnh.',
            'image.mimes' => 'Ảnh phải có định dạng: jpeg, png, jpg, gif.',
            'image.max' => 'Kích thước ảnh không được vượt quá 2MB.',
            'status.required' => 'Vui lòng chọn trạng thái.',
            'status.in' => 'Trạng thái không hợp lệ.',
        ];
    }
}
