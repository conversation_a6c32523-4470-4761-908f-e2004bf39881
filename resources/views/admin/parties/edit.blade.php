@extends('layouts/layoutMaster')

@section('title', 'Chỉnh S<PERSON><PERSON>')

@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/flatpickr/flatpickr.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss',
  'resources/css/parties.css'
])
@endsection

@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/flatpickr/flatpickr.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
])
@endsection

@section('page-script')
@vite([
  'resources/js/party-form.js'
])
@endsection

@section('content')
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">Chỉnh <PERSON><PERSON><PERSON> Sự: {{ $party->full_name }}</h5>
        <div>
          <a href="{{ route('admin.parties.show', $party) }}" class="btn btn-outline-info me-2">
            <i class="ri-eye-line me-1"></i>Xem chi tiết
          </a>
          <a href="{{ route('admin.parties.index') }}" class="btn btn-outline-secondary">
            <i class="ri-arrow-left-line me-1"></i>Quay lại
          </a>
        </div>
      </div>
      <div class="card-body">
        <form action="{{ route('admin.parties.update', $party) }}" method="POST" id="partyForm">
          @csrf
          @method('PUT')
          @include('admin.parties.partials.form')
          
          <div class="row mt-4">
            <div class="col-12">
              <button type="submit" class="btn btn-primary me-2">
                <i class="ri-save-line me-1"></i>Cập nhật
              </button>
              <a href="{{ route('admin.parties.index') }}" class="btn btn-outline-secondary">
                <i class="ri-close-line me-1"></i>Hủy
              </a>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
@endsection
