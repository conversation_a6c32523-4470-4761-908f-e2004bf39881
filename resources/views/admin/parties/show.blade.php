@extends('layouts/layoutMaster')

@section('title', '<PERSON> Tiết Đương Sự')

@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss',
  'resources/css/parties.css'
])
@endsection

@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
])
@endsection

@section('content')
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">Chi Tiết Đương Sự: {{ $party->full_name }}</h5>
        <div>
          <a href="{{ route('admin.parties.edit', $party) }}" class="btn btn-outline-warning me-2">
            <i class="ri-edit-line me-1"></i>Chỉnh sửa
          </a>
          <a href="{{ route('admin.parties.index') }}" class="btn btn-outline-secondary">
            <i class="ri-arrow-left-line me-1"></i>Quay lại
          </a>
        </div>
      </div>
      <div class="card-body">
        <div class="row">
          <!-- Thông tin cá nhân -->
          <div class="col-md-6">
            <div class="card h-100">
              <div class="card-header">
                <h6 class="card-title mb-0">
                  <i class="ri-user-line me-2"></i>Thông Tin Cá Nhân
                </h6>
              </div>
              <div class="card-body">
                <div class="row mb-3">
                  <div class="col-sm-4"><strong>Họ và tên:</strong></div>
                  <div class="col-sm-8">{{ $party->full_name }}</div>
                </div>
                <div class="row mb-3">
                  <div class="col-sm-4"><strong>Ngày sinh:</strong></div>
                  <div class="col-sm-8">
                    {{ $party->formatted_date_of_birth ?: 'Chưa có thông tin' }}
                    @if($party->age)
                      <span class="text-muted">({{ $party->age }} tuổi)</span>
                    @endif
                  </div>
                </div>
                <div class="row mb-3">
                  <div class="col-sm-4"><strong>Giới tính:</strong></div>
                  <div class="col-sm-8">{{ $party->gender_label }}</div>
                </div>
                <div class="row mb-3">
                  <div class="col-sm-4"><strong>CCCD/CMND:</strong></div>
                  <div class="col-sm-8">{{ $party->id_number ?: 'Chưa có thông tin' }}</div>
                </div>
                @if($party->id_issue_date)
                <div class="row mb-3">
                  <div class="col-sm-4"><strong>Ngày cấp:</strong></div>
                  <div class="col-sm-8">{{ $party->id_issue_date->format('d/m/Y') }}</div>
                </div>
                @endif
                @if($party->id_issue_place)
                <div class="row mb-3">
                  <div class="col-sm-4"><strong>Nơi cấp:</strong></div>
                  <div class="col-sm-8">{{ $party->id_issue_place }}</div>
                </div>
                @endif
              </div>
            </div>
          </div>

          <!-- Thông tin liên hệ -->
          <div class="col-md-6">
            <div class="card h-100">
              <div class="card-header">
                <h6 class="card-title mb-0">
                  <i class="ri-contacts-line me-2"></i>Thông Tin Liên Hệ
                </h6>
              </div>
              <div class="card-body">
                <div class="row mb-3">
                  <div class="col-sm-4"><strong>Điện thoại:</strong></div>
                  <div class="col-sm-8">{{ $party->phone_number ?: 'Chưa có thông tin' }}</div>
                </div>
                <div class="row mb-3">
                  <div class="col-sm-4"><strong>Email:</strong></div>
                  <div class="col-sm-8">{{ $party->email ?: 'Chưa có thông tin' }}</div>
                </div>
                <div class="row mb-3">
                  <div class="col-sm-4"><strong>Địa chỉ thường trú:</strong></div>
                  <div class="col-sm-8">{{ $party->permanent_address ?: 'Chưa có thông tin' }}</div>
                </div>
                @if($party->temporary_address)
                <div class="row mb-3">
                  <div class="col-sm-4"><strong>Địa chỉ tạm trú:</strong></div>
                  <div class="col-sm-8">{{ $party->temporary_address }}</div>
                </div>
                @endif
              </div>
            </div>
          </div>
        </div>

        <div class="row mt-4">
          <!-- Tình trạng hôn nhân -->
          <div class="col-md-6">
            <div class="card h-100">
              <div class="card-header">
                <h6 class="card-title mb-0">
                  <i class="ri-heart-line me-2"></i>Tình Trạng Hôn Nhân
                </h6>
              </div>
              <div class="card-body">
                <div class="row mb-3">
                  <div class="col-sm-4"><strong>Tình trạng:</strong></div>
                  <div class="col-sm-8">{{ $party->marital_status_label }}</div>
                </div>
                @if($party->is_married && $party->spouse_name)
                <div class="row mb-3">
                  <div class="col-sm-4"><strong>Tên vợ/chồng:</strong></div>
                  <div class="col-sm-8">{{ $party->spouse_name }}</div>
                </div>
                @if($party->spouse_date_of_birth)
                <div class="row mb-3">
                  <div class="col-sm-4"><strong>Ngày sinh:</strong></div>
                  <div class="col-sm-8">{{ $party->formatted_spouse_date_of_birth }}</div>
                </div>
                @endif
                @if($party->spouse_id_number)
                <div class="row mb-3">
                  <div class="col-sm-4"><strong>CCCD/CMND:</strong></div>
                  <div class="col-sm-8">{{ $party->spouse_id_number }}</div>
                </div>
                @endif
                @if($party->spouse_address)
                <div class="row mb-3">
                  <div class="col-sm-4"><strong>Địa chỉ:</strong></div>
                  <div class="col-sm-8">{{ $party->spouse_address }}</div>
                </div>
                @endif
                @endif
              </div>
            </div>
          </div>

          <!-- Giấy chứng nhận kết hôn -->
          @if($party->is_married && ($party->marriage_certificate_number || $party->marriage_certificate_date || $party->marriage_certificate_place))
          <div class="col-md-6">
            <div class="card h-100">
              <div class="card-header">
                <h6 class="card-title mb-0">
                  <i class="ri-file-text-line me-2"></i>Giấy Chứng Nhận Kết Hôn
                </h6>
              </div>
              <div class="card-body">
                @if($party->marriage_certificate_number)
                <div class="row mb-3">
                  <div class="col-sm-4"><strong>Số giấy CN:</strong></div>
                  <div class="col-sm-8">{{ $party->marriage_certificate_number }}</div>
                </div>
                @endif
                @if($party->marriage_certificate_date)
                <div class="row mb-3">
                  <div class="col-sm-4"><strong>Ngày cấp:</strong></div>
                  <div class="col-sm-8">{{ $party->marriage_certificate_date->format('d/m/Y') }}</div>
                </div>
                @endif
                @if($party->marriage_certificate_place)
                <div class="row mb-3">
                  <div class="col-sm-4"><strong>Nơi cấp:</strong></div>
                  <div class="col-sm-8">{{ $party->marriage_certificate_place }}</div>
                </div>
                @endif

              </div>
            </div>
          </div>
          @endif
        </div>

        <div class="row">
          <!-- Nghề nghiệp -->
          <div class="col-md-6">
            <div class="card h-100">
              <div class="card-header">
                <h6 class="card-title mb-0">
                  <i class="ri-briefcase-line me-2"></i>Nghề Nghiệp
                </h6>
              </div>
              <div class="card-body">
                <div class="row mb-3">
                  <div class="col-sm-4"><strong>Nghề nghiệp:</strong></div>
                  <div class="col-sm-8">{{ $party->occupation ?: 'Chưa có thông tin' }}</div>
                </div>
                <div class="row mb-3">
                  <div class="col-sm-4"><strong>Nơi làm việc:</strong></div>
                  <div class="col-sm-8">{{ $party->workplace ?: 'Chưa có thông tin' }}</div>
                </div>
                @if($party->workplace_address)
                <div class="row mb-3">
                  <div class="col-sm-4"><strong>Địa chỉ nơi làm việc:</strong></div>
                  <div class="col-sm-8">{{ $party->workplace_address }}</div>
                </div>
                @endif
              </div>
            </div>
          </div>
        </div>

        <div class="row mt-4">
          <!-- Vai trò và trạng thái -->
          <div class="col-md-6">
            <div class="card h-100">
              <div class="card-header">
                <h6 class="card-title mb-0">
                  <i class="ri-shield-user-line me-2"></i>Vai Trò & Trạng Thái
                </h6>
              </div>
              <div class="card-body">
                <div class="row mb-3">
                  <div class="col-sm-4"><strong>Vai trò trong vụ việc:</strong></div>
                  <div class="col-sm-8">{!! $party->role_badge !!}</div>
                </div>
                <div class="row mb-3">
                  <div class="col-sm-4"><strong>Trạng thái:</strong></div>
                  <div class="col-sm-8">{!! $party->status_badge !!}</div>
                </div>
                <div class="row mb-3">
                  <div class="col-sm-4"><strong>Ngày tạo:</strong></div>
                  <div class="col-sm-8">{{ $party->formatted_created_at }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Ghi chú -->
          <div class="col-md-6">
            <div class="card h-100">
              <div class="card-header">
                <h6 class="card-title mb-0">
                  <i class="ri-file-text-line me-2"></i>Ghi Chú
                </h6>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-12">
                    @if($party->notes)
                      <p class="mb-0">{{ $party->notes }}</p>
                    @else
                      <p class="text-muted mb-0">Không có ghi chú</p>
                    @endif
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
@endsection
