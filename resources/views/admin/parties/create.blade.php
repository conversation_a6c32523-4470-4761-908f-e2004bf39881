@extends('layouts/layoutMaster')

@section('title', 'Thê<PERSON>ớ<PERSON>')

@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/flatpickr/flatpickr.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss',
  'resources/css/parties.css'
])
@endsection

@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/flatpickr/flatpickr.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
])
@endsection

@section('page-script')
@vite([
  'resources/js/party-form.js'
])
@endsection

@section('content')
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">Thê<PERSON></h5>
        <a href="{{ route('admin.parties.index') }}" class="btn btn-outline-secondary">
          <i class="ri-arrow-left-line me-1"></i>Quay lại
        </a>
      </div>
      <div class="card-body">
        <form action="{{ route('admin.parties.store') }}" method="POST" id="partyForm">
          @csrf
          @include('admin.parties.partials.form')
          
          <div class="row mt-4">
            <div class="col-12">
              <button type="submit" class="btn btn-primary me-2">
                <i class="ri-save-line me-1"></i>Lưu
              </button>
              <a href="{{ route('admin.parties.index') }}" class="btn btn-outline-secondary">
                <i class="ri-close-line me-1"></i>Hủy
              </a>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
@endsection
