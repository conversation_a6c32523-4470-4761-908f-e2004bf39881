<!-- Thông tin cá nhân cơ bản -->
<div class="card mb-4">
  <div class="card-header">
    <h6 class="card-title mb-0">
      <i class="ri-user-line me-2"></i>Thông Tin Cá Nhân
    </h6>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="col-md-6">
        <div class="form-floating form-floating-outline mb-4">
          <input type="text" class="form-control @error('full_name') is-invalid @enderror"
                 id="full_name" name="full_name"
                 value="{{ old('full_name', isset($party) ? $party->full_name : '') }}"
                 placeholder="Nhập họ và tên">
          <label for="full_name">Họ và tên <span class="text-danger">*</span></label>
          @error('full_name')
            <div class="invalid-feedback">{{ $message }}</div>
          @enderror
        </div>
      </div>
      <div class="col-md-3">
        <div class="form-floating form-floating-outline mb-4">
          <input type="text" class="form-control flatpickr-date @error('date_of_birth') is-invalid @enderror"
                 id="date_of_birth" name="date_of_birth"
                 value="{{ old('date_of_birth', isset($party) && $party->date_of_birth ? $party->date_of_birth->format('d/m/Y') : '') }}"
                 placeholder="dd/mm/yyyy">
          <label for="date_of_birth">Ngày sinh</label>
          @error('date_of_birth')
            <div class="invalid-feedback">{{ $message }}</div>
          @enderror
        </div>
      </div>
      <div class="col-md-3">
        <div class="form-floating form-floating-outline mb-4">
          <select class="form-select @error('gender') is-invalid @enderror" id="gender" name="gender">
            <option value="">Chọn giới tính</option>
            @foreach(\App\Models\Party::getGenderOptions() as $value => $label)
              <option value="{{ $value }}" {{ old('gender', isset($party) ? $party->gender : '') == $value ? 'selected' : '' }}>
                {{ $label }}
              </option>
            @endforeach
          </select>
          <label for="gender">Giới tính</label>
          @error('gender')
            <div class="invalid-feedback">{{ $message }}</div>
          @enderror
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-md-4">
        <div class="form-floating form-floating-outline mb-4">
          <input type="text" class="form-control @error('id_number') is-invalid @enderror"
                 id="id_number" name="id_number"
                 value="{{ old('id_number', isset($party) ? $party->id_number : '') }}"
                 placeholder="Nhập số CCCD/CMND">
          <label for="id_number">Số CCCD/CMND</label>
          @error('id_number')
            <div class="invalid-feedback">{{ $message }}</div>
          @enderror
        </div>
      </div>
      <div class="col-md-4">
        <div class="form-floating form-floating-outline mb-4">
          <input type="text" class="form-control flatpickr-date @error('id_issue_date') is-invalid @enderror"
                 id="id_issue_date" name="id_issue_date"
                 value="{{ old('id_issue_date', isset($party) && $party->id_issue_date ? $party->id_issue_date->format('d/m/Y') : '') }}"
                 placeholder="dd/mm/yyyy">
          <label for="id_issue_date">Ngày cấp</label>
          @error('id_issue_date')
            <div class="invalid-feedback">{{ $message }}</div>
          @enderror
        </div>
      </div>
      <div class="col-md-4">
        <div class="form-floating form-floating-outline mb-4">
          <input type="text" class="form-control @error('id_issue_place') is-invalid @enderror"
                 id="id_issue_place" name="id_issue_place"
                 value="{{ old('id_issue_place', isset($party) ? $party->id_issue_place : '') }}"
                 placeholder="Nhập nơi cấp">
          <label for="id_issue_place">Nơi cấp</label>
          @error('id_issue_place')
            <div class="invalid-feedback">{{ $message }}</div>
          @enderror
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Thông tin liên hệ -->
<div class="card mb-4">
  <div class="card-header">
    <h6 class="card-title mb-0">
      <i class="ri-contacts-line me-2"></i>Thông Tin Liên Hệ
    </h6>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="col-md-6">
        <div class="form-floating form-floating-outline mb-4">
          <textarea class="form-control @error('permanent_address') is-invalid @enderror"
                    id="permanent_address" name="permanent_address"
                    placeholder="Nhập địa chỉ thường trú" rows="3">{{ old('permanent_address', isset($party) ? $party->permanent_address : '') }}</textarea>
          <label for="permanent_address">Địa chỉ thường trú</label>
          @error('permanent_address')
            <div class="invalid-feedback">{{ $message }}</div>
          @enderror
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-floating form-floating-outline mb-4">
          <textarea class="form-control @error('temporary_address') is-invalid @enderror"
                    id="temporary_address" name="temporary_address"
                    placeholder="Nhập địa chỉ tạm trú" rows="3">{{ old('temporary_address', isset($party) ? $party->temporary_address : '') }}</textarea>
          <label for="temporary_address">Địa chỉ tạm trú</label>
          @error('temporary_address')
            <div class="invalid-feedback">{{ $message }}</div>
          @enderror
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-md-6">
        <div class="form-floating form-floating-outline mb-4">
          <input type="text" class="form-control @error('phone_number') is-invalid @enderror"
                 id="phone_number" name="phone_number"
                 value="{{ old('phone_number', isset($party) ? $party->phone_number : '') }}"
                 placeholder="Nhập số điện thoại">
          <label for="phone_number">Số điện thoại</label>
          @error('phone_number')
            <div class="invalid-feedback">{{ $message }}</div>
          @enderror
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-floating form-floating-outline mb-4">
          <input type="email" class="form-control @error('email') is-invalid @enderror"
                 id="email" name="email"
                 value="{{ old('email', isset($party) ? $party->email : '') }}"
                 placeholder="Nhập email">
          <label for="email">Email</label>
          @error('email')
            <div class="invalid-feedback">{{ $message }}</div>
          @enderror
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Tình trạng hôn nhân -->
<div class="card mb-4">
  <div class="card-header">
    <h6 class="card-title mb-0">
      <i class="ri-heart-line me-2"></i>Tình Trạng Hôn Nhân
    </h6>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="col-md-6">
        <div class="form-floating form-floating-outline mb-4">
          <select class="form-select select2 @error('marital_status') is-invalid @enderror"
                  id="marital_status" name="marital_status">
            <option value="">Chọn tình trạng hôn nhân</option>
            @foreach(\App\Models\Party::getMaritalStatusOptions() as $value => $label)
              <option value="{{ $value }}" {{ old('marital_status', isset($party) ? $party->marital_status : '') == $value ? 'selected' : '' }}>
                {{ $label }}
              </option>
            @endforeach
          </select>
          <label for="marital_status">Tình trạng hôn nhân <span class="text-danger">*</span></label>
          @error('marital_status')
            <div class="invalid-feedback">{{ $message }}</div>
          @enderror
        </div>
      </div>
    </div>

    <!-- Thông tin vợ/chồng (hiển thị khi đã kết hôn) -->
    <div id="spouseInfo" style="display: none;">
      <div class="row">
        <div class="col-md-6">
          <div class="form-floating form-floating-outline mb-4">
            <input type="text" class="form-control @error('spouse_name') is-invalid @enderror"
                   id="spouse_name" name="spouse_name"
                   value="{{ old('spouse_name', isset($party) ? $party->spouse_name : '') }}"
                   placeholder="Nhập tên vợ/chồng">
            <label for="spouse_name">Tên vợ/chồng <span class="text-danger">*</span></label>
            @error('spouse_name')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-floating form-floating-outline mb-4">
            <input type="text" class="form-control flatpickr-date @error('spouse_date_of_birth') is-invalid @enderror"
                   id="spouse_date_of_birth" name="spouse_date_of_birth"
                   value="{{ old('spouse_date_of_birth', isset($party) && $party->spouse_date_of_birth ? $party->spouse_date_of_birth->format('d/m/Y') : '') }}"
                   placeholder="dd/mm/yyyy">
            <label for="spouse_date_of_birth">Ngày sinh vợ/chồng <span class="text-danger">*</span></label>
            @error('spouse_date_of_birth')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-md-6">
          <div class="form-floating form-floating-outline mb-4">
            <input type="text" class="form-control @error('spouse_id_number') is-invalid @enderror"
                   id="spouse_id_number" name="spouse_id_number"
                   value="{{ old('spouse_id_number', isset($party) ? $party->spouse_id_number : '') }}"
                   placeholder="Nhập số CCCD/CMND vợ/chồng">
            <label for="spouse_id_number">Số CCCD/CMND vợ/chồng</label>
            @error('spouse_id_number')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-floating form-floating-outline mb-4">
            <textarea class="form-control @error('spouse_address') is-invalid @enderror"
                      id="spouse_address" name="spouse_address"
                      placeholder="Nhập địa chỉ vợ/chồng" rows="3">{{ old('spouse_address', isset($party) ? $party->spouse_address : '') }}</textarea>
            <label for="spouse_address">Địa chỉ vợ/chồng</label>
            @error('spouse_address')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
        </div>
      </div>
    </div>

    <!-- Thông tin giấy chứng nhận kết hôn -->
    <div id="marriageCertificateSection" style="display: none;">
      <div class="row">
        <div class="col-12">
          <h6 class="text-body fw-medium mb-3">
            <i class="ri-file-text-line me-2"></i>Thông Tin Giấy Chứng Nhận Kết Hôn
          </h6>
        </div>
      </div>

      <div class="row">
        <div class="col-md-6">
          <div class="form-floating form-floating-outline mb-4">
            <input type="text" class="form-control @error('marriage_certificate_number') is-invalid @enderror"
                   id="marriage_certificate_number" name="marriage_certificate_number"
                   value="{{ old('marriage_certificate_number', isset($party) ? $party->marriage_certificate_number : '') }}"
                   placeholder="Nhập số giấy chứng nhận kết hôn">
            <label for="marriage_certificate_number">Số giấy chứng nhận kết hôn <span class="text-danger">*</span></label>
            @error('marriage_certificate_number')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-floating form-floating-outline mb-4">
            <input type="text" class="form-control flatpickr-date @error('marriage_certificate_date') is-invalid @enderror"
                   id="marriage_certificate_date" name="marriage_certificate_date"
                   value="{{ old('marriage_certificate_date', isset($party) && $party->marriage_certificate_date ? $party->marriage_certificate_date->format('d/m/Y') : '') }}"
                   placeholder="dd/mm/yyyy">
            <label for="marriage_certificate_date">Ngày cấp <span class="text-danger">*</span></label>
            @error('marriage_certificate_date')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-md-6">
          <div class="form-floating form-floating-outline mb-4">
            <input type="text" class="form-control @error('marriage_certificate_place') is-invalid @enderror"
                   id="marriage_certificate_place" name="marriage_certificate_place"
                   value="{{ old('marriage_certificate_place', isset($party) ? $party->marriage_certificate_place : '') }}"
                   placeholder="Nhập nơi cấp giấy chứng nhận kết hôn">
            <label for="marriage_certificate_place">Nơi cấp <span class="text-danger">*</span></label>
            @error('marriage_certificate_place')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
        </div>

      </div>
    </div>
  </div>
</div>

<!-- Nghề nghiệp và nơi làm việc -->
<div class="card mb-4">
  <div class="card-header">
    <h6 class="card-title mb-0">
      <i class="ri-briefcase-line me-2"></i>Nghề Nghiệp
    </h6>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="col-md-6">
        <div class="form-floating form-floating-outline mb-4">
          <input type="text" class="form-control @error('occupation') is-invalid @enderror"
                 id="occupation" name="occupation"
                 value="{{ old('occupation', isset($party) ? $party->occupation : '') }}"
                 placeholder="Nhập nghề nghiệp">
          <label for="occupation">Nghề nghiệp</label>
          @error('occupation')
            <div class="invalid-feedback">{{ $message }}</div>
          @enderror
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-floating form-floating-outline mb-4">
          <input type="text" class="form-control @error('workplace') is-invalid @enderror"
                 id="workplace" name="workplace"
                 value="{{ old('workplace', isset($party) ? $party->workplace : '') }}"
                 placeholder="Nhập nơi làm việc">
          <label for="workplace">Nơi làm việc</label>
          @error('workplace')
            <div class="invalid-feedback">{{ $message }}</div>
          @enderror
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-12">
        <div class="form-floating form-floating-outline mb-4">
          <textarea class="form-control @error('workplace_address') is-invalid @enderror"
                    id="workplace_address" name="workplace_address"
                    placeholder="Nhập địa chỉ nơi làm việc" rows="3">{{ old('workplace_address', isset($party) ? $party->workplace_address : '') }}</textarea>
          <label for="workplace_address">Địa chỉ nơi làm việc</label>
          @error('workplace_address')
            <div class="invalid-feedback">{{ $message }}</div>
          @enderror
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Vai trò trong vụ việc và trạng thái -->
<div class="card mb-4">
  <div class="card-header">
    <h6 class="card-title mb-0">
      <i class="ri-shield-user-line me-2"></i>Vai Trò & Trạng Thái
    </h6>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="col-md-6">
        <div class="form-floating form-floating-outline mb-4">
          <select class="form-select select2 @error('role_in_case') is-invalid @enderror"
                  id="role_in_case" name="role_in_case">
            <option value="">Chọn vai trò trong vụ việc</option>
            @foreach(\App\Models\Party::getRoleInCaseOptions() as $value => $label)
              <option value="{{ $value }}" {{ old('role_in_case', isset($party) ? $party->role_in_case : '') == $value ? 'selected' : '' }}>
                {{ $label }}
              </option>
            @endforeach
          </select>
          <label for="role_in_case">Vai trò trong vụ việc <span class="text-danger">*</span></label>
          @error('role_in_case')
            <div class="invalid-feedback">{{ $message }}</div>
          @enderror
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-floating form-floating-outline mb-4">
          <select class="form-select @error('status') is-invalid @enderror" id="status" name="status">
            @foreach(\App\Models\Party::getStatusOptions() as $value => $label)
              <option value="{{ $value }}" {{ old('status', isset($party) ? $party->status : 'active') == $value ? 'selected' : '' }}>
                {{ $label }}
              </option>
            @endforeach
          </select>
          <label for="status">Trạng thái <span class="text-danger">*</span></label>
          @error('status')
            <div class="invalid-feedback">{{ $message }}</div>
          @enderror
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Ghi chú -->
<div class="card mb-4">
  <div class="card-header">
    <h6 class="card-title mb-0">
      <i class="ri-file-text-line me-2"></i>Ghi Chú
    </h6>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="col-12">
        <div class="form-floating form-floating-outline mb-4">
          <textarea class="form-control @error('notes') is-invalid @enderror"
                    id="notes" name="notes"
                    placeholder="Nhập ghi chú bổ sung" rows="4">{{ old('notes', isset($party) ? $party->notes : '') }}</textarea>
          <label for="notes">Ghi chú bổ sung</label>
          @error('notes')
            <div class="invalid-feedback">{{ $message }}</div>
          @enderror
        </div>
      </div>
    </div>
  </div>
</div>
