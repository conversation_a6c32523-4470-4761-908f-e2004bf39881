@extends('layouts/layoutMaster')

@section('title', '<PERSON><PERSON><PERSON><PERSON>')

@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.scss',
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss',
  'resources/css/parties.css'
])
@endsection

@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
])
@endsection

@section('page-script')
@vite([
  'resources/js/datatables-config.js',
  'resources/js/party-management.js'
])
@endsection

@section('content')
<!-- Statistics Cards -->
<div class="row g-6 mb-6">
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">Tổng Đương Sự</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-2">{{ $totalCount }}</h4>
            </div>
            <small class="mb-0">Tất cả đương sự</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-primary rounded-3">
              <div class="ri-user-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">Đang Hoạt Động</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-2">{{ $activeCount }}</h4>
            </div>
            <small class="mb-0">Đương sự đang hoạt động</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-success rounded-3">
              <div class="ri-check-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">Nguyên Đơn</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-2">{{ $plaintiffCount }}</h4>
            </div>
            <small class="mb-0">Số lượng nguyên đơn</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-info rounded-3">
              <div class="ri-user-star-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">Bị Đơn</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-2">{{ $defendantCount }}</h4>
            </div>
            <small class="mb-0">Số lượng bị đơn</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-warning rounded-3">
              <div class="ri-user-forbid-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Filters -->
<div class="card mb-6">
  <div class="card-body">
    <div class="row g-3">
      <div class="col-md-3">
        <div class="form-floating form-floating-outline">
          <select id="StatusFilter" class="form-select">
            <option value="">Tất cả trạng thái</option>
            <option value="active">Đang hoạt động</option>
            <option value="inactive">Không hoạt động</option>
          </select>
          <label for="StatusFilter">Trạng thái</label>
        </div>
      </div>
      <div class="col-md-3">
        <div class="form-floating form-floating-outline">
          <select id="RoleFilter" class="form-select">
            <option value="">Tất cả vai trò</option>
            <option value="plaintiff">Nguyên đơn</option>
            <option value="defendant">Bị đơn</option>
            <option value="related_party">Người có quyền lợi nghĩa vụ liên quan</option>
          </select>
          <label for="RoleFilter">Vai trò trong vụ việc</label>
        </div>
      </div>
      <div class="col-md-3">
        <div class="form-floating form-floating-outline">
          <select id="MaritalStatusFilter" class="form-select">
            <option value="">Tất cả tình trạng</option>
            <option value="single">Độc thân</option>
            <option value="married">Đã kết hôn</option>
            <option value="divorced">Đã ly hôn</option>
            <option value="widowed">Góa phụ/Góa chồng</option>
          </select>
          <label for="MaritalStatusFilter">Tình trạng hôn nhân</label>
        </div>
      </div>
      <div class="col-md-3">
        <button type="button" class="btn btn-outline-secondary" id="clearFilters">
          <i class="ri-refresh-line me-1"></i>Xóa bộ lọc
        </button>
      </div>
    </div>
  </div>
</div>

<!-- DataTable -->
<div class="card">
  <div class="card-header d-flex justify-content-between align-items-center">
    <h5 class="card-title mb-0">Danh Sách Đương Sự</h5>
    <a href="{{ route('admin.parties.create') }}" class="btn btn-primary">
      <i class="ri-add-line me-1"></i>Thêm Mới
    </a>
  </div>
  <div class="card-datatable table-responsive">
    <table class="datatables-parties table table-bordered">
      <thead>
        <tr>
          <th></th>
          <th>ID</th>
          <th>Họ và tên</th>
          <th>CCCD/CMND</th>
          <th>Điện thoại</th>
          <th>Tuổi</th>
          <th>Vai trò</th>
          <th>Tình trạng HN</th>
          <th>Nghề nghiệp</th>
          <th>Trạng thái</th>
          <th>Ngày tạo</th>
          <th>Thao tác</th>
        </tr>
      </thead>
    </table>
  </div>
</div>

<!-- Quick View Modal -->
<div class="modal fade" id="quickViewModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-lg modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Thông Tin Đương Sự</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body" id="quickViewContent">
        <!-- Content will be loaded here -->
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Đóng</button>
      </div>
    </div>
  </div>
</div>

@if(session('success'))
<script>
document.addEventListener('DOMContentLoaded', function() {
  Swal.fire({
    title: 'Thành công!',
    text: '{{ session('success') }}',
    icon: 'success',
    customClass: {
      confirmButton: 'btn btn-primary'
    }
  });
});
</script>
@endif

@if(session('error'))
<script>
document.addEventListener('DOMContentLoaded', function() {
  Swal.fire({
    title: 'Lỗi!',
    text: '{{ session('error') }}',
    icon: 'error',
    customClass: {
      confirmButton: 'btn btn-danger'
    }
  });
});
</script>
@endif
@endsection
