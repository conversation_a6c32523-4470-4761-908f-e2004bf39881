@extends('layouts/layoutMaster')

@section('title', 'Chi Tiết Công Chứng Viên')

@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss',
  'resources/css/notaries.css'
])
@endsection

@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
])
@endsection

@section('content')
<div class="row">
  <div class="col-12">
    <!-- Header -->
    <div class="card mb-6">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">Chi Tiết Công Chứng Viên</h5>
        <div class="d-flex gap-2">
          <a href="{{ route('admin.notaries.edit', $notary) }}" class="btn btn-primary">
            <i class="ri-edit-line me-1"></i>Chỉnh sửa
          </a>
          <a href="{{ route('admin.notaries.index') }}" class="btn btn-outline-secondary">
            <i class="ri-arrow-left-line me-1"></i>Quay lại
          </a>
        </div>
      </div>
    </div>

    <!-- Notary Information -->
    <div class="row">
      <div class="col-md-8">
        <div class="card mb-6">
          <div class="card-header">
            <h6 class="card-title mb-0">Thông Tin Công Chứng Viên</h6>
          </div>
          <div class="card-body">
            <div class="row g-4">
              <div class="col-md-6">
                <label class="form-label fw-medium">Họ và tên:</label>
                <p class="mb-0">{{ $notary->full_name }}</p>
              </div>
              <div class="col-md-6">
                <label class="form-label fw-medium">Chức vụ:</label>
                <p class="mb-0">{{ $notary->position ?: '-' }}</p>
              </div>
              <div class="col-md-6">
                <label class="form-label fw-medium">Số chứng chỉ:</label>
                <p class="mb-0">{{ $notary->certificate_number }}</p>
              </div>
              <div class="col-md-6">
                <label class="form-label fw-medium">Ngày cấp chứng chỉ:</label>
                <p class="mb-0">{{ $notary->formatted_certificate_issue_date ?: '-' }}</p>
              </div>
              <div class="col-12">
                <label class="form-label fw-medium">Nơi cấp chứng chỉ:</label>
                <p class="mb-0">{{ $notary->certificate_issue_place ?: '-' }}</p>
              </div>
              <div class="col-12">
                <label class="form-label fw-medium">Phòng công chứng:</label>
                <p class="mb-0">
                  @if($notary->notaryOffice)
                    <a href="{{ route('admin.notary-offices.show', $notary->notaryOffice) }}">
                      {{ $notary->notaryOffice->name }} ({{ $notary->notaryOffice->code }})
                    </a>
                  @else
                    -
                  @endif
                </p>
              </div>
              @if($notary->address)
              <div class="col-12">
                <label class="form-label fw-medium">Địa chỉ:</label>
                <p class="mb-0">{{ $notary->address }}</p>
              </div>
              @endif
              <div class="col-md-6">
                <label class="form-label fw-medium">Số điện thoại:</label>
                <p class="mb-0">{{ $notary->phone ?: '-' }}</p>
              </div>
              <div class="col-md-6">
                <label class="form-label fw-medium">Email:</label>
                <p class="mb-0">{{ $notary->email ?: '-' }}</p>
              </div>
              <div class="col-md-6">
                <label class="form-label fw-medium">Trạng thái:</label>
                <p class="mb-0">{!! $notary->status_badge !!}</p>
              </div>
              @if($notary->notes)
              <div class="col-12">
                <label class="form-label fw-medium">Ghi chú:</label>
                <p class="mb-0">{{ $notary->notes }}</p>
              </div>
              @endif
              <div class="col-md-6">
                <label class="form-label fw-medium">Ngày tạo:</label>
                <p class="mb-0">{{ $notary->formatted_created_at }}</p>
              </div>
              <div class="col-md-6">
                <label class="form-label fw-medium">Cập nhật lần cuối:</label>
                <p class="mb-0">{{ $notary->formatted_updated_at }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-4">
        <!-- Image -->
        <div class="card mb-6">
          <div class="card-header">
            <h6 class="card-title mb-0">Ảnh Đại Diện</h6>
          </div>
          <div class="card-body text-center">
            @if($notary->image_url)
              <img src="{{ $notary->image_url }}" alt="{{ $notary->full_name }}" class="img-fluid rounded" style="max-height: 300px;">
            @else
              <div class="avatar avatar-xl mx-auto mb-3">
                <div class="avatar-initial bg-label-secondary rounded">
                  <i class="ri-user-line ri-36px"></i>
                </div>
              </div>
              <p class="text-muted">Chưa có ảnh đại diện</p>
            @endif
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
          <div class="card-header">
            <h6 class="card-title mb-0">Thao Tác Nhanh</h6>
          </div>
          <div class="card-body">
            <div class="d-grid gap-2">
              <a href="{{ route('admin.notaries.edit', $notary) }}" class="btn btn-primary">
                <i class="ri-edit-line me-1"></i>Chỉnh sửa thông tin
              </a>
              
              @if($notary->notaryOffice)
              <a href="{{ route('admin.notary-offices.show', $notary->notaryOffice) }}" class="btn btn-outline-info">
                <i class="ri-building-line me-1"></i>Xem phòng công chứng
              </a>
              @endif
              
              <button type="button" class="btn btn-outline-{{ $notary->status === 'active' ? 'warning' : 'success' }} toggle-status" 
                      data-id="{{ $notary->id }}" 
                      data-status="{{ $notary->status }}">
                <i class="ri-{{ $notary->status === 'active' ? 'pause' : 'play' }}-line me-1"></i>
                {{ $notary->status === 'active' ? 'Tạm ngưng' : 'Kích hoạt' }}
              </button>
              
              <button type="button" class="btn btn-outline-danger delete-record" 
                      data-id="{{ $notary->id }}" 
                      data-name="{{ $notary->full_name }}">
                <i class="ri-delete-bin-line me-1"></i>Xóa công chứng viên
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle status functionality
    document.addEventListener('click', function(e) {
        if (e.target.closest('.toggle-status')) {
            const button = e.target.closest('.toggle-status');
            const id = button.dataset.id;
            const currentStatus = button.dataset.status;
            
            Swal.fire({
                title: 'Xác nhận thay đổi trạng thái',
                text: `Bạn có chắc chắn muốn ${currentStatus === 'active' ? 'tạm ngưng' : 'kích hoạt'} công chứng viên này?`,
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'Xác nhận',
                cancelButtonText: 'Hủy bỏ'
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch(`/admin/notaries/${id}/toggle-status`, {
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                            'Content-Type': 'application/json'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire('Thành công!', data.message, 'success').then(() => {
                                location.reload();
                            });
                        } else {
                            Swal.fire('Lỗi!', data.message, 'error');
                        }
                    })
                    .catch(error => {
                        Swal.fire('Lỗi!', 'Có lỗi xảy ra khi thay đổi trạng thái.', 'error');
                    });
                }
            });
        }
        
        // Delete functionality
        if (e.target.closest('.delete-record')) {
            const button = e.target.closest('.delete-record');
            const id = button.dataset.id;
            const name = button.dataset.name;
            
            Swal.fire({
                title: 'Xác nhận xóa',
                text: `Bạn có chắc chắn muốn xóa công chứng viên "${name}"?`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Xóa',
                cancelButtonText: 'Hủy bỏ',
                confirmButtonColor: '#d33'
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch(`/admin/notaries/${id}`, {
                        method: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                            'Content-Type': 'application/json'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire('Đã xóa!', data.message, 'success').then(() => {
                                window.location.href = '{{ route('admin.notaries.index') }}';
                            });
                        } else {
                            Swal.fire('Lỗi!', data.message, 'error');
                        }
                    })
                    .catch(error => {
                        Swal.fire('Lỗi!', 'Có lỗi xảy ra khi xóa công chứng viên.', 'error');
                    });
                }
            });
        }
    });
});
</script>
@endsection
