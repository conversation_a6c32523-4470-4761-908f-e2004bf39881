<div class="row">
  <!-- Basic Information -->
  <div class="col-md-8">
    <div class="card mb-6">
      <div class="card-header">
        <h6 class="card-title mb-0">Thông Tin Cơ Bản</h6>
      </div>
      <div class="card-body">
        <div class="row g-4">
          <!-- Full Name -->
          <div class="col-md-6">
            <label class="form-label" for="full_name">Họ và tên <span class="text-danger">*</span></label>
            <input type="text" 
                   id="full_name" 
                   name="full_name" 
                   class="form-control @error('full_name') is-invalid @enderror" 
                   placeholder="Nhập họ và tên"
                   value="{{ old('full_name', $notary->full_name ?? '') }}"
                   required>
            @error('full_name')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>

          <!-- Position -->
          <div class="col-md-6">
            <label class="form-label" for="position">Chứ<PERSON> vụ</label>
            <input type="text" 
                   id="position" 
                   name="position" 
                   class="form-control @error('position') is-invalid @enderror" 
                   placeholder="Nhập chức vụ"
                   value="{{ old('position', $notary->position ?? '') }}">
            @error('position')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>

          <!-- Certificate Number -->
          <div class="col-md-6">
            <label class="form-label" for="certificate_number">Số chứng chỉ <span class="text-danger">*</span></label>
            <input type="text" 
                   id="certificate_number" 
                   name="certificate_number" 
                   class="form-control @error('certificate_number') is-invalid @enderror" 
                   placeholder="Nhập số chứng chỉ hành nghề"
                   value="{{ old('certificate_number', $notary->certificate_number ?? '') }}"
                   required>
            @error('certificate_number')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>

          <!-- Certificate Issue Date -->
          <div class="col-md-6">
            <label class="form-label" for="certificate_issue_date">Ngày cấp chứng chỉ</label>
            <input type="date" 
                   id="certificate_issue_date" 
                   name="certificate_issue_date" 
                   class="form-control @error('certificate_issue_date') is-invalid @enderror"
                   value="{{ old('certificate_issue_date', isset($notary) && $notary->certificate_issue_date ? $notary->certificate_issue_date->format('Y-m-d') : '') }}">
            @error('certificate_issue_date')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>

          <!-- Certificate Issue Place -->
          <div class="col-12">
            <label class="form-label" for="certificate_issue_place">Nơi cấp chứng chỉ</label>
            <input type="text" 
                   id="certificate_issue_place" 
                   name="certificate_issue_place" 
                   class="form-control @error('certificate_issue_place') is-invalid @enderror" 
                   placeholder="Nhập nơi cấp chứng chỉ"
                   value="{{ old('certificate_issue_place', $notary->certificate_issue_place ?? '') }}">
            @error('certificate_issue_place')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>

          <!-- Notary Office -->
          <div class="col-12">
            <label class="form-label" for="notary_office_id">Phòng công chứng</label>
            <select id="notary_office_id" 
                    name="notary_office_id" 
                    class="form-select @error('notary_office_id') is-invalid @enderror">
              <option value="">Chọn phòng công chứng</option>
              @foreach($notaryOffices as $office)
                <option value="{{ $office->id }}" 
                        {{ old('notary_office_id', $notary->notary_office_id ?? request('notary_office_id')) == $office->id ? 'selected' : '' }}>
                  {{ $office->name }} ({{ $office->code }})
                </option>
              @endforeach
            </select>
            @error('notary_office_id')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>

          <!-- Address -->
          <div class="col-12">
            <label class="form-label" for="address">Địa chỉ</label>
            <textarea id="address" 
                      name="address" 
                      class="form-control @error('address') is-invalid @enderror" 
                      rows="3"
                      placeholder="Nhập địa chỉ">{{ old('address', $notary->address ?? '') }}</textarea>
            @error('address')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>

          <!-- Phone -->
          <div class="col-md-6">
            <label class="form-label" for="phone">Số điện thoại</label>
            <input type="text" 
                   id="phone" 
                   name="phone" 
                   class="form-control @error('phone') is-invalid @enderror" 
                   placeholder="Nhập số điện thoại"
                   value="{{ old('phone', $notary->phone ?? '') }}">
            @error('phone')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>

          <!-- Email -->
          <div class="col-md-6">
            <label class="form-label" for="email">Email</label>
            <input type="email" 
                   id="email" 
                   name="email" 
                   class="form-control @error('email') is-invalid @enderror" 
                   placeholder="Nhập email"
                   value="{{ old('email', $notary->email ?? '') }}">
            @error('email')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>

          <!-- Notes -->
          <div class="col-12">
            <label class="form-label" for="notes">Ghi chú</label>
            <textarea id="notes" 
                      name="notes" 
                      class="form-control @error('notes') is-invalid @enderror" 
                      rows="4"
                      placeholder="Nhập ghi chú">{{ old('notes', $notary->notes ?? '') }}</textarea>
            @error('notes')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Image and Settings -->
  <div class="col-md-4">
    <!-- Image Upload -->
    <div class="card mb-6">
      <div class="card-header">
        <h6 class="card-title mb-0">Ảnh Đại Diện</h6>
      </div>
      <div class="card-body">
        <!-- Current Image -->
        @if(isset($notary) && $notary->image_url)
        <div class="current-image mb-3 text-center">
          <img src="{{ $notary->image_url }}" alt="Current image" class="img-fluid rounded" style="max-height: 200px;">
          <div class="mt-2">
            <div class="form-check">
              <input class="form-check-input" type="checkbox" id="remove_image" name="remove_image" value="1">
              <label class="form-check-label" for="remove_image">
                Xóa ảnh hiện tại
              </label>
            </div>
          </div>
        </div>
        @endif

        <!-- File Input -->
        <div class="mb-3">
          <label class="form-label" for="image">Chọn ảnh mới</label>
          <input type="file" 
                 id="image" 
                 name="image" 
                 class="form-control @error('image') is-invalid @enderror"
                 accept="image/*">
          @error('image')
            <div class="invalid-feedback">{{ $message }}</div>
          @enderror
          <div class="form-text">
            Định dạng: JPG, PNG, GIF. Kích thước tối đa: 2MB
          </div>
        </div>

        <!-- Image Preview -->
        <div id="imagePreview" class="text-center" style="display: none;">
          <img id="previewImg" src="" alt="Preview" class="img-fluid rounded" style="max-height: 200px;">
        </div>
      </div>
    </div>

    <!-- Settings -->
    <div class="card">
      <div class="card-header">
        <h6 class="card-title mb-0">Cài Đặt</h6>
      </div>
      <div class="card-body">
        <!-- Status -->
        <div class="mb-4">
          <label class="form-label" for="status">Trạng thái <span class="text-danger">*</span></label>
          <select id="status" 
                  name="status" 
                  class="form-select @error('status') is-invalid @enderror"
                  required>
            <option value="">Chọn trạng thái</option>
            <option value="active" {{ old('status', $notary->status ?? 'active') === 'active' ? 'selected' : '' }}>
              Hoạt động
            </option>
            <option value="inactive" {{ old('status', $notary->status ?? '') === 'inactive' ? 'selected' : '' }}>
              Không hoạt động
            </option>
          </select>
          @error('status')
            <div class="invalid-feedback">{{ $message }}</div>
          @enderror
        </div>

        <!-- Submit Buttons -->
        <div class="d-grid gap-2">
          <button type="submit" class="btn btn-primary">
            <i class="ri-save-line me-1"></i>
            {{ isset($notary) ? 'Cập nhật' : 'Tạo mới' }}
          </button>
          <a href="{{ route('admin.notaries.index') }}" class="btn btn-outline-secondary">
            <i class="ri-close-line me-1"></i>Hủy bỏ
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
