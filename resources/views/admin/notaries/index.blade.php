@extends('layouts/layoutMaster')

@section('title', 'Q<PERSON>ản L<PERSON>ng <PERSON>ng Viên')

@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.scss',
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss',
  'resources/css/notaries.css'
])
@endsection

@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
])
@endsection

@section('page-script')
@vite([
  'resources/js/datatables-config.js',
  'resources/js/notary-management.js'
])
@endsection

@section('content')
<!-- Statistics Cards -->
<div class="row g-6 mb-6">
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">Tổng Công Chứng Viên</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-2">{{ $totalCount }}</h4>
            </div>
            <small class="mb-0">Tất cả công chứng viên</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-primary rounded-3">
              <div class="ri-user-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">Đang Hoạt Động</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-2">{{ $activeCount }}</h4>
            </div>
            <small class="mb-0">Công chứng viên đang hoạt động</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-success rounded-3">
              <div class="ri-check-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">Không Hoạt Động</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-2">{{ $inactiveCount }}</h4>
            </div>
            <small class="mb-0">Công chứng viên không hoạt động</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-secondary rounded-3">
              <div class="ri-pause-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">Tỷ Lệ Hoạt Động</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-2">{{ $totalCount > 0 ? round(($activeCount / $totalCount) * 100, 1) : 0 }}%</h4>
            </div>
            <small class="mb-0">Công chứng viên đang hoạt động</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-info rounded-3">
              <div class="ri-pie-chart-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Notaries List -->
<div class="card">
  <div class="card-header d-flex justify-content-between align-items-center">
    <h5 class="card-title mb-0">Danh Sách Công Chứng Viên</h5>
    <a href="{{ route('admin.notaries.create') }}" class="btn btn-primary">
      <i class="ri-add-line me-1"></i>Thêm Công Chứng Viên
    </a>
  </div>
  
  <!-- Filters -->
  <div class="card-body border-bottom">
    <div class="row g-3">
      <div class="col-md-3">
        <label class="form-label">Tìm kiếm</label>
        <input type="text" id="searchInput" class="form-control" placeholder="Tìm theo tên, số chứng chỉ...">
      </div>
      <div class="col-md-3">
        <label class="form-label">Phòng công chứng</label>
        <select id="notaryOfficeFilter" class="form-select">
          <option value="">Tất cả phòng công chứng</option>
          @foreach($notaryOffices as $office)
            <option value="{{ $office->id }}">{{ $office->name }}</option>
          @endforeach
        </select>
      </div>
      <div class="col-md-2">
        <label class="form-label">Trạng thái</label>
        <select id="statusFilter" class="form-select">
          <option value="">Tất cả trạng thái</option>
          <option value="active">Hoạt động</option>
          <option value="inactive">Không hoạt động</option>
        </select>
      </div>
      <div class="col-md-2">
        <label class="form-label">&nbsp;</label>
        <div class="d-flex gap-2">
          <button type="button" id="filterBtn" class="btn btn-outline-primary">
            <i class="ri-search-line me-1"></i>Lọc
          </button>
          <button type="button" id="resetBtn" class="btn btn-outline-secondary">
            <i class="ri-refresh-line me-1"></i>Đặt lại
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="card-datatable table-responsive">
    <table id="notariesTable" class="datatables-notaries table table-sm">
      <thead>
        <tr>
          <th>ID</th>
          <th>Họ và Tên</th>
          <th>Chức vụ</th>
          <th>Số Chứng Chỉ</th>
          <th>Phòng Công Chứng</th>
          <th>Điện Thoại</th>
          <th>Email</th>
          <th>Trạng Thái</th>
          <th>Ngày Tạo</th>
          <th>Thao Tác</th>
        </tr>
      </thead>
    </table>
  </div>
</div>

@if(session('success'))
<script>
document.addEventListener('DOMContentLoaded', function() {
    Swal.fire({
        icon: 'success',
        title: 'Thành công!',
        text: '{{ session('success') }}',
        timer: 3000,
        showConfirmButton: false
    });
});
</script>
@endif

@if(session('error'))
<script>
document.addEventListener('DOMContentLoaded', function() {
    Swal.fire({
        icon: 'error',
        title: 'Lỗi!',
        text: '{{ session('error') }}',
        timer: 5000,
        showConfirmButton: true
    });
});
</script>
@endif

@endsection
