@extends('layouts/layoutMaster')

@section('title', 'Chi Tiết Phòng Công Chứng')

@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss',
  'resources/css/notary-offices.css'
])
@endsection

@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
])
@endsection

@section('content')
<div class="row">
  <div class="col-12">
    <!-- Header -->
    <div class="card mb-6">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">Chi Tiết Phòng Công Chứng</h5>
        <div class="d-flex gap-2">
          <a href="{{ route('admin.notary-offices.edit', $notaryOffice) }}" class="btn btn-primary">
            <i class="ri-edit-line me-1"></i>Chỉnh sửa
          </a>
          <a href="{{ route('admin.notary-offices.index') }}" class="btn btn-outline-secondary">
            <i class="ri-arrow-left-line me-1"></i>Quay lại
          </a>
        </div>
      </div>
    </div>

    <!-- Office Information -->
    <div class="row">
      <div class="col-md-8">
        <div class="card mb-6">
          <div class="card-header">
            <h6 class="card-title mb-0">Thông Tin Phòng Công Chứng</h6>
          </div>
          <div class="card-body">
            <div class="row g-4">
              <div class="col-md-6">
                <label class="form-label fw-medium">Tên phòng công chứng:</label>
                <p class="mb-0">{{ $notaryOffice->name }}</p>
              </div>
              <div class="col-md-6">
                <label class="form-label fw-medium">Mã số:</label>
                <p class="mb-0">{{ $notaryOffice->code }}</p>
              </div>
              <div class="col-12">
                <label class="form-label fw-medium">Địa chỉ:</label>
                <p class="mb-0">{{ $notaryOffice->address }}</p>
              </div>
              <div class="col-md-6">
                <label class="form-label fw-medium">Số điện thoại:</label>
                <p class="mb-0">{{ $notaryOffice->phone ?: '-' }}</p>
              </div>
              <div class="col-md-6">
                <label class="form-label fw-medium">Email:</label>
                <p class="mb-0">{{ $notaryOffice->email ?: '-' }}</p>
              </div>
              <div class="col-md-6">
                <label class="form-label fw-medium">Website:</label>
                <p class="mb-0">
                  @if($notaryOffice->website)
                    <a href="{{ $notaryOffice->website }}" target="_blank">{{ $notaryOffice->website }}</a>
                  @else
                    -
                  @endif
                </p>
              </div>
              <div class="col-md-6">
                <label class="form-label fw-medium">Trạng thái:</label>
                <p class="mb-0">{!! $notaryOffice->status_badge !!}</p>
              </div>
              @if($notaryOffice->description)
              <div class="col-12">
                <label class="form-label fw-medium">Mô tả:</label>
                <p class="mb-0">{{ $notaryOffice->description }}</p>
              </div>
              @endif
              <div class="col-md-6">
                <label class="form-label fw-medium">Ngày tạo:</label>
                <p class="mb-0">{{ $notaryOffice->formatted_created_at }}</p>
              </div>
              <div class="col-md-6">
                <label class="form-label fw-medium">Cập nhật lần cuối:</label>
                <p class="mb-0">{{ $notaryOffice->formatted_updated_at }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-4">
        <!-- Image -->
        <div class="card mb-6">
          <div class="card-header">
            <h6 class="card-title mb-0">Ảnh Đại Diện</h6>
          </div>
          <div class="card-body text-center">
            @if($notaryOffice->image_url)
              <img src="{{ $notaryOffice->image_url }}" alt="{{ $notaryOffice->name }}" class="img-fluid rounded" style="max-height: 300px;">
            @else
              <div class="avatar avatar-xl mx-auto mb-3">
                <div class="avatar-initial bg-label-secondary rounded">
                  <i class="ri-building-line ri-36px"></i>
                </div>
              </div>
              <p class="text-muted">Chưa có ảnh đại diện</p>
            @endif
          </div>
        </div>

        <!-- Statistics -->
        <div class="card">
          <div class="card-header">
            <h6 class="card-title mb-0">Thống Kê</h6>
          </div>
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-3">
              <span>Tổng công chứng viên:</span>
              <span class="badge bg-label-primary">{{ $notaryOffice->notaries->count() }}</span>
            </div>
            <div class="d-flex justify-content-between align-items-center mb-3">
              <span>Đang hoạt động:</span>
              <span class="badge bg-label-success">{{ $notaryOffice->notaries->where('status', 'active')->count() }}</span>
            </div>
            <div class="d-flex justify-content-between align-items-center">
              <span>Không hoạt động:</span>
              <span class="badge bg-label-secondary">{{ $notaryOffice->notaries->where('status', 'inactive')->count() }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Notaries List -->
    @if($notaryOffice->notaries->count() > 0)
    <div class="card">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h6 class="card-title mb-0">Danh Sách Công Chứng Viên</h6>
        <a href="{{ route('admin.notaries.create', ['notary_office_id' => $notaryOffice->id]) }}" class="btn btn-sm btn-primary">
          <i class="ri-add-line me-1"></i>Thêm công chứng viên
        </a>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-sm">
            <thead>
              <tr>
                <th>Họ và Tên</th>
                <th>Chức vụ</th>
                <th>Số chứng chỉ</th>
                <th>Điện thoại</th>
                <th>Trạng thái</th>
                <th>Thao tác</th>
              </tr>
            </thead>
            <tbody>
              @foreach($notaryOffice->notaries as $notary)
              <tr>
                <td>{{ $notary->full_name }}</td>
                <td>{{ $notary->position ?: '-' }}</td>
                <td>{{ $notary->certificate_number }}</td>
                <td>{{ $notary->phone ?: '-' }}</td>
                <td>{!! $notary->status_badge !!}</td>
                <td>
                  <div class="d-flex gap-1">
                    <a href="{{ route('admin.notaries.show', $notary) }}" class="btn btn-sm btn-outline-info" title="Xem chi tiết">
                      <i class="ri-eye-line"></i>
                    </a>
                    <a href="{{ route('admin.notaries.edit', $notary) }}" class="btn btn-sm btn-outline-primary" title="Chỉnh sửa">
                      <i class="ri-edit-line"></i>
                    </a>
                  </div>
                </td>
              </tr>
              @endforeach
            </tbody>
          </table>
        </div>
      </div>
    </div>
    @else
    <div class="card">
      <div class="card-body text-center py-5">
        <div class="avatar avatar-xl mx-auto mb-3">
          <div class="avatar-initial bg-label-secondary rounded">
            <i class="ri-user-line ri-36px"></i>
          </div>
        </div>
        <h6 class="mb-2">Chưa có công chứng viên</h6>
        <p class="text-muted mb-4">Phòng công chứng này chưa có công chứng viên nào.</p>
        <a href="{{ route('admin.notaries.create', ['notary_office_id' => $notaryOffice->id]) }}" class="btn btn-primary">
          <i class="ri-add-line me-1"></i>Thêm công chứng viên đầu tiên
        </a>
      </div>
    </div>
    @endif
  </div>
</div>
@endsection
