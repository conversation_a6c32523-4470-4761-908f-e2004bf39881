<div class="d-flex align-items-center gap-2">
  <!-- View Button -->
  <a href="{{ route('admin.notary-offices.show', $item->id) }}" 
     class="btn btn-sm btn-outline-info" 
     title="Xem chi tiết">
    <i class="ri-eye-line"></i>
  </a>

  <!-- Edit Button -->
  <a href="{{ route('admin.notary-offices.edit', $item->id) }}" 
     class="btn btn-sm btn-outline-primary" 
     title="Chỉnh sửa">
    <i class="ri-edit-line"></i>
  </a>

  <!-- Status Toggle Button -->
  <button type="button" 
          class="btn btn-sm btn-outline-{{ $item->status === 'active' ? 'warning' : 'success' }} toggle-status" 
          data-id="{{ $item->id }}" 
          data-status="{{ $item->status }}"
          title="{{ $item->status === 'active' ? 'Tạm ngưng' : 'Kích hoạt' }}">
    <i class="ri-{{ $item->status === 'active' ? 'pause' : 'play' }}-line"></i>
  </button>

  <!-- Delete Button -->
  <button type="button" 
          class="btn btn-sm btn-outline-danger delete-record" 
          data-id="{{ $item->id }}" 
          data-name="{{ $item->name }}" 
          title="Xóa">
    <i class="ri-delete-bin-line"></i>
  </button>
</div>
