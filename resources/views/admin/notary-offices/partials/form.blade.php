<div class="row">
  <!-- Basic Information -->
  <div class="col-md-8">
    <div class="card mb-6">
      <div class="card-header">
        <h6 class="card-title mb-0">Thông Tin Cơ Bản</h6>
      </div>
      <div class="card-body">
        <div class="row g-4">
          <!-- Name -->
          <div class="col-md-6">
            <label class="form-label" for="name">Tên phòng công chứng <span class="text-danger">*</span></label>
            <input type="text" 
                   id="name" 
                   name="name" 
                   class="form-control @error('name') is-invalid @enderror" 
                   placeholder="Nhập tên phòng công chứng"
                   value="{{ old('name', $notaryOffice->name ?? '') }}"
                   required>
            @error('name')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>

          <!-- Code -->
          <div class="col-md-6">
            <label class="form-label" for="code">Mã số <span class="text-danger">*</span></label>
            <input type="text" 
                   id="code" 
                   name="code" 
                   class="form-control @error('code') is-invalid @enderror" 
                   placeholder="Nhập mã số phòng công chứng"
                   value="{{ old('code', $notaryOffice->code ?? '') }}"
                   required>
            @error('code')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>

          <!-- Address -->
          <div class="col-12">
            <label class="form-label" for="address">Địa chỉ <span class="text-danger">*</span></label>
            <textarea id="address" 
                      name="address" 
                      class="form-control @error('address') is-invalid @enderror" 
                      rows="3"
                      placeholder="Nhập địa chỉ phòng công chứng"
                      required>{{ old('address', $notaryOffice->address ?? '') }}</textarea>
            @error('address')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>

          <!-- Phone -->
          <div class="col-md-6">
            <label class="form-label" for="phone">Số điện thoại</label>
            <input type="text" 
                   id="phone" 
                   name="phone" 
                   class="form-control @error('phone') is-invalid @enderror" 
                   placeholder="Nhập số điện thoại"
                   value="{{ old('phone', $notaryOffice->phone ?? '') }}">
            @error('phone')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>

          <!-- Email -->
          <div class="col-md-6">
            <label class="form-label" for="email">Email</label>
            <input type="email" 
                   id="email" 
                   name="email" 
                   class="form-control @error('email') is-invalid @enderror" 
                   placeholder="Nhập email"
                   value="{{ old('email', $notaryOffice->email ?? '') }}">
            @error('email')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>

          <!-- Website -->
          <div class="col-12">
            <label class="form-label" for="website">Website</label>
            <input type="url" 
                   id="website" 
                   name="website" 
                   class="form-control @error('website') is-invalid @enderror" 
                   placeholder="Nhập website (http://... hoặc https://...)"
                   value="{{ old('website', $notaryOffice->website ?? '') }}">
            @error('website')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>

          <!-- Description -->
          <div class="col-12">
            <label class="form-label" for="description">Mô tả</label>
            <textarea id="description" 
                      name="description" 
                      class="form-control @error('description') is-invalid @enderror" 
                      rows="4"
                      placeholder="Nhập mô tả về phòng công chứng">{{ old('description', $notaryOffice->description ?? '') }}</textarea>
            @error('description')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Image and Settings -->
  <div class="col-md-4">
    <!-- Image Upload -->
    <div class="card mb-6">
      <div class="card-header">
        <h6 class="card-title mb-0">Ảnh Đại Diện</h6>
      </div>
      <div class="card-body">
        <!-- Current Image -->
        @if(isset($notaryOffice) && $notaryOffice->image_url)
        <div class="current-image mb-3 text-center">
          <img src="{{ $notaryOffice->image_url }}" alt="Current image" class="img-fluid rounded" style="max-height: 200px;">
          <div class="mt-2">
            <div class="form-check">
              <input class="form-check-input" type="checkbox" id="remove_image" name="remove_image" value="1">
              <label class="form-check-label" for="remove_image">
                Xóa ảnh hiện tại
              </label>
            </div>
          </div>
        </div>
        @endif

        <!-- File Input -->
        <div class="mb-3">
          <label class="form-label" for="image">Chọn ảnh mới</label>
          <input type="file" 
                 id="image" 
                 name="image" 
                 class="form-control @error('image') is-invalid @enderror"
                 accept="image/*">
          @error('image')
            <div class="invalid-feedback">{{ $message }}</div>
          @enderror
          <div class="form-text">
            Định dạng: JPG, PNG, GIF. Kích thước tối đa: 2MB
          </div>
        </div>

        <!-- Image Preview -->
        <div id="imagePreview" class="text-center" style="display: none;">
          <img id="previewImg" src="" alt="Preview" class="img-fluid rounded" style="max-height: 200px;">
        </div>
      </div>
    </div>

    <!-- Settings -->
    <div class="card">
      <div class="card-header">
        <h6 class="card-title mb-0">Cài Đặt</h6>
      </div>
      <div class="card-body">
        <!-- Status -->
        <div class="mb-4">
          <label class="form-label" for="status">Trạng thái <span class="text-danger">*</span></label>
          <select id="status" 
                  name="status" 
                  class="form-select @error('status') is-invalid @enderror"
                  required>
            <option value="">Chọn trạng thái</option>
            <option value="active" {{ old('status', $notaryOffice->status ?? 'active') === 'active' ? 'selected' : '' }}>
              Hoạt động
            </option>
            <option value="inactive" {{ old('status', $notaryOffice->status ?? '') === 'inactive' ? 'selected' : '' }}>
              Không hoạt động
            </option>
          </select>
          @error('status')
            <div class="invalid-feedback">{{ $message }}</div>
          @enderror
        </div>

        <!-- Sort Order -->
        <div class="mb-4">
          <label class="form-label" for="sort_order">Thứ tự sắp xếp</label>
          <input type="number" 
                 id="sort_order" 
                 name="sort_order" 
                 class="form-control @error('sort_order') is-invalid @enderror" 
                 placeholder="Nhập thứ tự sắp xếp"
                 min="0"
                 value="{{ old('sort_order', $notaryOffice->sort_order ?? '') }}">
          @error('sort_order')
            <div class="invalid-feedback">{{ $message }}</div>
          @enderror
          <div class="form-text">
            Để trống để tự động sắp xếp
          </div>
        </div>

        <!-- Submit Buttons -->
        <div class="d-grid gap-2">
          <button type="submit" class="btn btn-primary">
            <i class="ri-save-line me-1"></i>
            {{ isset($notaryOffice) ? 'Cập nhật' : 'Tạo mới' }}
          </button>
          <a href="{{ route('admin.notary-offices.index') }}" class="btn btn-outline-secondary">
            <i class="ri-close-line me-1"></i>Hủy bỏ
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
