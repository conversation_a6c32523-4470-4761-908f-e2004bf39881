@extends('layouts/layoutMaster')

@section('title', 'Quản L<PERSON>à<PERSON>')

@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.scss',
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss',
  'resources/css/assets.css'
])
@endsection

@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
])
@endsection

@section('page-script')
@vite([
  'resources/js/datatables-config.js',
  'resources/js/asset-management.js'
])
@endsection

@section('content')
{{-- Flash Messages --}}
@if(session('success'))
<div class="alert alert-success alert-dismissible fade show" role="alert">
  <i class="ri-check-line me-2"></i>{{ session('success') }}
  <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
@endif

@if(session('error'))
<div class="alert alert-danger alert-dismissible fade show" role="alert">
  <i class="ri-error-warning-line me-2"></i>{{ session('error') }}
  <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
@endif

<!-- Statistics Cards -->
<div class="row g-6 mb-6">
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">Tổng Tài Sản</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-2">{{ $totalCount }}</h4>
            </div>
            <small class="mb-0">Tất cả tài sản</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-primary rounded-3">
              <div class="ri-building-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">Đang Sử Dụng</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-2">{{ $activeCount }}</h4>
            </div>
            <small class="mb-0">Tài sản đang hoạt động</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-success rounded-3">
              <div class="ri-check-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">Không Sử Dụng</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-2">{{ $inactiveCount }}</h4>
            </div>
            <small class="mb-0">Tài sản tạm dừng</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-secondary rounded-3">
              <div class="ri-pause-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">Đã Bán</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-2">{{ $soldCount }}</h4>
            </div>
            <small class="mb-0">Tài sản đã bán</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-warning rounded-3">
              <div class="ri-money-dollar-circle-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Asset Management Table -->
<div class="card">
  <div class="card-header border-bottom">
    <div class="d-flex justify-content-between align-items-center">
      <h5 class="card-title mb-0">Danh Sách Tài Sản</h5>
      <div class="d-flex gap-3">
        <a href="{{ route('admin.assets.create') }}" class="btn btn-primary">
          <i class="ri-add-line me-1"></i>Thêm Tài Sản
        </a>
        <a href="{{ route('admin.assets.export') }}" class="btn btn-outline-success">
          <i class="ri-download-line me-1"></i>Xuất Excel
        </a>
      </div>
    </div>
  </div>

  <!-- Filters -->
  <div class="card-body border-bottom">
    <div class="row g-3">
      <div class="col-md-3">
        <label class="form-label">Trạng thái</label>
        <select id="statusFilter" class="form-select">
          <option value="">Tất cả trạng thái</option>
          <option value="active">Đang sử dụng</option>
          <option value="inactive">Không sử dụng</option>
          <option value="sold">Đã bán</option>
          <option value="transferred">Đã chuyển nhượng</option>
        </select>
      </div>
      <div class="col-md-3">
        <label class="form-label">Loại tài sản</label>
        <select id="assetTypeFilter" class="form-select">
          <option value="">Tất cả loại</option>
          @foreach($assetTypes as $type)
            <option value="{{ $type->id }}">{{ $type->name }}</option>
          @endforeach
        </select>
      </div>
      <div class="col-md-3">
        <label class="form-label">Tỉnh/Thành phố</label>
        <input type="text" id="provinceFilter" class="form-control" placeholder="Nhập tỉnh/thành phố">
      </div>
      <div class="col-md-3">
        <label class="form-label">&nbsp;</label>
        <div class="d-flex gap-2">
          <button type="button" id="applyFilters" class="btn btn-primary">
            <i class="ri-search-line me-1"></i>Lọc
          </button>
          <button type="button" id="clearFilters" class="btn btn-outline-secondary">
            <i class="ri-refresh-line me-1"></i>Xóa
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="card-datatable table-responsive">
    <table id="assetsTable" class="datatables-assets table table-sm">
      <thead>
        <tr>
          <th><input type="checkbox" id="selectAll" class="form-check-input"></th>
          <th>Mã Tài Sản</th>
          <th>Tên Tài Sản</th>
          <th>Loại</th>
          <th>Địa Chỉ</th>
          <th>Diện Tích</th>
          <th>Giá Trị</th>
          <th>Trạng Thái</th>
          <th>Ngày Tiếp Nhận</th>
          <th>Ngày Tạo</th>
          <th>Thao Tác</th>
        </tr>
      </thead>
    </table>
  </div>
</div>



<!-- Bulk Actions -->
<div class="d-none" id="bulkActions">
  <div class="card mt-3">
    <div class="card-body">
      <div class="d-flex justify-content-between align-items-center">
        <span id="selectedCount">0 tài sản được chọn</span>
        <div class="d-flex gap-2">
          <button type="button" id="bulkDelete" class="btn btn-danger btn-sm">
            <i class="ri-delete-bin-line me-1"></i>Xóa Đã Chọn
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
@endsection
