{{-- resources/views/admin/assets/partials/form.blade.php --}}
<div class="row">
  <div class="col-md-8">
    <!-- Basic Information -->
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="card-title mb-0">Thông Tin Cơ Bản</h5>
      </div>
      <div class="card-body">
        <div class="row g-3">
          <div class="col-md-6">
            <div class="form-floating form-floating-outline">
              <input type="text"
                     class="form-control @error('asset_code') is-invalid @enderror"
                     id="asset_code"
                     name="asset_code"
                     value="{{ old('asset_code', isset($asset) ? ($asset->asset_code ?? '') : '') }}"
                     placeholder="Mã tài sản (tự động tạo nếu để trống)">
              <label for="asset_code">M<PERSON> Tà<PERSON>ản</label>
              @error('asset_code')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>

          <div class="col-md-6">
            <div class="form-floating form-floating-outline">
              <select class="form-select @error('asset_type_id') is-invalid @enderror"
                      id="asset_type_id"
                      name="asset_type_id">
                <option value="">Chọn loại tài sản</option>
                @foreach($assetTypes as $type)
                  <option value="{{ $type->id }}"
                          {{ old('asset_type_id', isset($asset) ? ($asset->asset_type_id ?? '') : '') == $type->id ? 'selected' : '' }}>
                    {{ $type->name }}
                  </option>
                @endforeach
              </select>
              <label for="asset_type_id">Loại Tài Sản</label>
              @error('asset_type_id')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>

          <div class="col-md-12">
            <div class="form-floating form-floating-outline">
              <input type="text"
                     class="form-control @error('name') is-invalid @enderror"
                     id="name"
                     name="name"
                     value="{{ old('name', isset($asset) ? ($asset->name ?? '') : '') }}"
                     placeholder="Nhập tên tài sản">
              <label for="name">Tên Tài Sản *</label>
              @error('name')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>

          <div class="col-md-12">
            <div class="form-floating form-floating-outline">
              <textarea class="form-control @error('description') is-invalid @enderror"
                        id="description"
                        name="description"
                        rows="3"
                        placeholder="Nhập mô tả tài sản">{{ old('description', isset($asset) ? ($asset->description ?? '') : '') }}</textarea>
              <label for="description">Mô Tả</label>
              @error('description')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Land Use Rights Information -->
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="card-title mb-0">Thông Tin Quyền Sử Dụng Đất</h5>
      </div>
      <div class="card-body">
        <div class="row g-3">
          <div class="col-md-12">
            <div class="form-floating form-floating-outline">
              <textarea class="form-control @error('land_origin_source') is-invalid @enderror"
                        id="land_origin_source"
                        name="land_origin_source"
                        rows="3"
                        placeholder="Nhập nguồn gốc sử dụng đất">{{ old('land_origin_source', isset($asset) ? ($asset->land_origin_source ?? '') : '') }}</textarea>
              <label for="land_origin_source">Nguồn Gốc Sử Dụng Đất</label>
              @error('land_origin_source')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>

          <div class="col-md-12">
            <div class="form-floating form-floating-outline">
              <textarea class="form-control @error('transfer_recognition') is-invalid @enderror"
                        id="transfer_recognition"
                        name="transfer_recognition"
                        rows="3"
                        placeholder="Nhập thông tin chuyển nhượng">{{ old('transfer_recognition', isset($asset) ? ($asset->transfer_recognition ?? '') : '') }}</textarea>
              <label for="transfer_recognition">Nhận Chuyển Nhượng Đất Được Công Nhận QSDĐ</label>
              @error('transfer_recognition')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>

          <div class="col-md-6">
            <div class="form-floating form-floating-outline">
              <textarea class="form-control @error('owner_info') is-invalid @enderror"
                        id="owner_info"
                        name="owner_info"
                        rows="3"
                        placeholder="Nhập thông tin gia chủ">{{ old('owner_info', isset($asset) ? ($asset->owner_info ?? '') : '') }}</textarea>
              <label for="owner_info">Thông Tin Gia Chủ, Thửa Đất</label>
              @error('owner_info')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>

          <div class="col-md-6">
            <div class="form-floating form-floating-outline">
              <input type="text"
                     class="form-control @error('map_sheet_number') is-invalid @enderror"
                     id="map_sheet_number"
                     name="map_sheet_number"
                     value="{{ old('map_sheet_number', isset($asset) ? ($asset->map_sheet_number ?? '') : '') }}"
                     placeholder="Nhập tờ bản đồ số">
              <label for="map_sheet_number">Tờ Bản Đồ Số</label>
              @error('map_sheet_number')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Address Information -->
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="card-title mb-0">Thông Tin Địa Chỉ</h5>
      </div>
      <div class="card-body">
        <div class="row g-3">
          <div class="col-md-12">
            <div class="form-floating form-floating-outline">
              <input type="text"
                     class="form-control @error('address') is-invalid @enderror"
                     id="address"
                     name="address"
                     value="{{ old('address', isset($asset) ? ($asset->address ?? '') : '') }}"
                     placeholder="Nhập địa chỉ cụ thể">
              <label for="address">Địa Chỉ Cụ Thể</label>
              @error('address')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>

          <div class="col-md-4">
            <div class="form-floating form-floating-outline">
              <input type="text"
                     class="form-control @error('ward') is-invalid @enderror"
                     id="ward"
                     name="ward"
                     value="{{ old('ward', isset($asset) ? ($asset->ward ?? '') : '') }}"
                     placeholder="Nhập xã/phường">
              <label for="ward">Xã/Phường</label>
              @error('ward')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>

          <div class="col-md-4">
            <div class="form-floating form-floating-outline">
              <input type="text"
                     class="form-control @error('district') is-invalid @enderror"
                     id="district"
                     name="district"
                     value="{{ old('district', isset($asset) ? ($asset->district ?? '') : '') }}"
                     placeholder="Nhập quận/huyện">
              <label for="district">Quận/Huyện</label>
              @error('district')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>

          <div class="col-md-4">
            <div class="form-floating form-floating-outline">
              <input type="text"
                     class="form-control @error('province') is-invalid @enderror"
                     id="province"
                     name="province"
                     value="{{ old('province', isset($asset) ? ($asset->province ?? '') : '') }}"
                     placeholder="Nhập tỉnh/thành phố">
              <label for="province">Tỉnh/Thành Phố</label>
              @error('province')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Area and Usage Information -->
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="card-title mb-0">Thông Tin Diện Tích & Sử Dụng</h5>
      </div>
      <div class="card-body">
        <div class="row g-3">
          <div class="col-md-4">
            <div class="form-floating form-floating-outline">
              <input type="number"
                     class="form-control @error('total_area') is-invalid @enderror"
                     id="total_area"
                     name="total_area"
                     step="0.01"
                     min="0"
                     value="{{ old('total_area', isset($asset) ? ($asset->total_area ?? '') : '') }}"
                     placeholder="Nhập tổng diện tích">
              <label for="total_area">Tổng Diện Tích (m²)</label>
              @error('total_area')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>

          <div class="col-md-4">
            <div class="form-floating form-floating-outline">
              <input type="number"
                     class="form-control @error('residential_area') is-invalid @enderror"
                     id="residential_area"
                     name="residential_area"
                     step="0.01"
                     min="0"
                     value="{{ old('residential_area', isset($asset) ? ($asset->residential_area ?? '') : '') }}"
                     placeholder="Nhập diện tích đất ở">
              <label for="residential_area">Diện Tích Đất Ở (m²)</label>
              @error('residential_area')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>

          <div class="col-md-4">
            <div class="form-floating form-floating-outline">
              <input type="number"
                     class="form-control @error('agricultural_area') is-invalid @enderror"
                     id="agricultural_area"
                     name="agricultural_area"
                     step="0.01"
                     min="0"
                     value="{{ old('agricultural_area', isset($asset) ? ($asset->agricultural_area ?? '') : '') }}"
                     placeholder="Nhập diện tích đất nông nghiệp">
              <label for="agricultural_area">Diện Tích Đất Nông Nghiệp (m²)</label>
              @error('agricultural_area')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>

          <div class="col-md-6">
            <div class="form-floating form-floating-outline">
              <select class="form-select @error('usage_form') is-invalid @enderror"
                      id="usage_form"
                      name="usage_form">
                <option value="">Chọn hình thức sử dụng</option>
                <option value="Sử dụng riêng" {{ old('usage_form', isset($asset) ? ($asset->usage_form ?? '') : '') == 'Sử dụng riêng' ? 'selected' : '' }}>Sử dụng riêng</option>
                <option value="Sử dụng chung" {{ old('usage_form', isset($asset) ? ($asset->usage_form ?? '') : '') == 'Sử dụng chung' ? 'selected' : '' }}>Sử dụng chung</option>
              </select>
              <label for="usage_form">Hình Thức Sử Dụng</label>
              @error('usage_form')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>

          <div class="col-md-6">
            <div class="form-floating form-floating-outline">
              <textarea class="form-control @error('usage_purpose') is-invalid @enderror"
                        id="usage_purpose"
                        name="usage_purpose"
                        rows="2"
                        placeholder="Nhập mục đích sử dụng">{{ old('usage_purpose', isset($asset) ? ($asset->usage_purpose ?? '') : '') }}</textarea>
              <label for="usage_purpose">Mục Đích Sử Dụng</label>
              @error('usage_purpose')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>

          <div class="col-md-12">
            <div class="form-floating form-floating-outline">
              <textarea class="form-control @error('usage_term') is-invalid @enderror"
                        id="usage_term"
                        name="usage_term"
                        rows="2"
                        placeholder="Nhập thời hạn sử dụng đất">{{ old('usage_term', isset($asset) ? ($asset->usage_term ?? '') : '') }}</textarea>
              <label for="usage_term">Thời Hạn Sử Dụng Đất</label>
              @error('usage_term')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="col-md-4">
    <!-- Status and Metadata -->
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="card-title mb-0">Trạng Thái & Thông Tin</h5>
      </div>
      <div class="card-body">
        <div class="row g-3">
          <div class="col-md-12">
            <div class="form-floating form-floating-outline">
              <select class="form-select @error('status') is-invalid @enderror"
                      id="status"
                      name="status">
                <option value="active" {{ old('status', isset($asset) ? ($asset->status ?? 'active') : 'active') == 'active' ? 'selected' : '' }}>Đang sử dụng</option>
                <option value="inactive" {{ old('status', isset($asset) ? ($asset->status ?? '') : '') == 'inactive' ? 'selected' : '' }}>Không sử dụng</option>
                <option value="sold" {{ old('status', isset($asset) ? ($asset->status ?? '') : '') == 'sold' ? 'selected' : '' }}>Đã bán</option>
                <option value="transferred" {{ old('status', isset($asset) ? ($asset->status ?? '') : '') == 'transferred' ? 'selected' : '' }}>Đã chuyển nhượng</option>
              </select>
              <label for="status">Trạng Thái *</label>
              @error('status')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>

          <div class="col-md-12">
            <div class="form-floating form-floating-outline">
              <input type="number"
                     class="form-control @error('estimated_value') is-invalid @enderror"
                     id="estimated_value"
                     name="estimated_value"
                     step="1000"
                     min="0"
                     value="{{ old('estimated_value', isset($asset) ? ($asset->estimated_value ?? '') : '') }}"
                     placeholder="Nhập giá trị ước tính">
              <label for="estimated_value">Giá Trị Ước Tính (VND)</label>
              @error('estimated_value')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>

          <div class="col-md-12">
            <div class="form-floating form-floating-outline">
              <input type="date"
                     class="form-control @error('acquisition_date') is-invalid @enderror"
                     id="acquisition_date"
                     name="acquisition_date"
                     value="{{ old('acquisition_date', isset($asset) && $asset && $asset->acquisition_date ? $asset->acquisition_date->format('Y-m-d') : '') }}"
              >
              <label for="acquisition_date">Ngày Tiếp Nhận</label>
              @error('acquisition_date')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>

          <div class="col-md-12">
            <div class="form-floating form-floating-outline">
              <textarea class="form-control @error('notes') is-invalid @enderror"
                        id="notes"
                        name="notes"
                        rows="4"
                        placeholder="Nhập ghi chú">{{ old('notes', isset($asset) ? ($asset->notes ?? '') : '') }}</textarea>
              <label for="notes">Ghi Chú</label>
              @error('notes')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- File Uploads -->
    <div class="card">
      <div class="card-header">
        <h5 class="card-title mb-0">Tài Liệu & Hình Ảnh</h5>
      </div>
      <div class="card-body">
        <div class="row g-3">
          <div class="col-md-12">
            <label class="form-label">Tài Liệu Đính Kèm</label>
            <input type="file"
                   class="form-control @error('document_files.*') is-invalid @enderror"
                   id="document_files"
                   name="document_files[]"
                   multiple
                   accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png">
            <div class="form-text">
              Chấp nhận: PDF, DOC, DOCX, XLS, XLSX, JPG, JPEG, PNG. Tối đa 10MB mỗi file.
            </div>
            @error('document_files.*')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>

          <div class="col-md-12">
            <label class="form-label">Hình Ảnh</label>
            <input type="file"
                   class="form-control @error('image_files.*') is-invalid @enderror"
                   id="image_files"
                   name="image_files[]"
                   multiple
                   accept="image/*">
            <div class="form-text">
              Chấp nhận: JPEG, PNG, JPG, GIF. Tối đa 5MB mỗi file.
            </div>
            @error('image_files.*')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
