@extends('layouts/layoutMaster')

@section('title', 'Thêm Tài <PERSON>ản <PERSON>')

@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss',
  'resources/css/assets.css'
])
@endsection

@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
])
@endsection

@section('page-script')
@vite([
  'resources/js/asset-form.js'
])
@endsection

@section('content')
{{-- Flash Messages --}}
@if(session('success'))
<div class="alert alert-success alert-dismissible fade show" role="alert">
  <i class="ri-check-line me-2"></i>{{ session('success') }}
  <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
@endif

@if(session('error'))
<div class="alert alert-danger alert-dismissible fade show" role="alert">
  <i class="ri-error-warning-line me-2"></i>{{ session('error') }}
  <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
@endif

<div class="d-flex justify-content-between align-items-center mb-4">
  <div>
    <h4 class="mb-1">Thêm Tài Sản Mới</h4>
    <p class="mb-0">Nhập thông tin chi tiết để tạo tài sản mới</p>
  </div>
  <div>
    <a href="{{ route('admin.assets.index') }}" class="btn btn-outline-secondary">
      <i class="ri-arrow-left-line me-1"></i>Quay Lại
    </a>
  </div>
</div>

<form action="{{ route('admin.assets.store') }}" method="POST" enctype="multipart/form-data">
  @csrf
  @include('admin.assets.partials.form')

  <div class="d-flex justify-content-end gap-3 mt-4">
    <a href="{{ route('admin.assets.index') }}" class="btn btn-outline-secondary">
      <i class="ri-close-line me-1"></i>Hủy
    </a>
    <button type="submit" class="btn btn-primary">
      <i class="ri-save-line me-1"></i>Lưu Tài Sản
    </button>
  </div>
</form>
@endsection
