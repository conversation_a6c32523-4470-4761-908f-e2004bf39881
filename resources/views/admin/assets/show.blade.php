@extends('layouts/layoutMaster')

@section('title', 'Chi Tiết Tài Sản')

@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss',
  'resources/css/assets.css'
])
@endsection

@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
])
@endsection

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
  <div>
    <h4 class="mb-1">Chi Tiết Tài Sản</h4>
    <p class="mb-0">Thông tin chi tiết của tài sản: <strong>{{ $asset->name }}</strong></p>
  </div>
  <div class="d-flex gap-2">
    <a href="{{ route('admin.assets.edit', $asset->id) }}" class="btn btn-primary">
      <i class="ri-edit-line me-1"></i>Chỉnh Sửa
    </a>
    <a href="{{ route('admin.assets.index') }}" class="btn btn-outline-secondary">
      <i class="ri-arrow-left-line me-1"></i>Quay Lại
    </a>
  </div>
</div>

<div class="row">
  <div class="col-md-8">
    <!-- Basic Information -->
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="card-title mb-0">Thông Tin Cơ Bản</h5>
      </div>
      <div class="card-body">
        <div class="row g-3">
          <div class="col-md-6">
            <label class="form-label fw-medium">Mã Tài Sản:</label>
            <p class="mb-0">{{ $asset->asset_code }}</p>
          </div>
          <div class="col-md-6">
            <label class="form-label fw-medium">Loại Tài Sản:</label>
            <p class="mb-0">{{ $asset->assetType ? $asset->assetType->name : 'Chưa phân loại' }}</p>
          </div>
          <div class="col-md-12">
            <label class="form-label fw-medium">Tên Tài Sản:</label>
            <p class="mb-0">{{ $asset->name }}</p>
          </div>
          @if($asset->description)
          <div class="col-md-12">
            <label class="form-label fw-medium">Mô Tả:</label>
            <p class="mb-0">{{ $asset->description }}</p>
          </div>
          @endif
        </div>
      </div>
    </div>

    <!-- Land Use Rights Information -->
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="card-title mb-0">Thông Tin Quyền Sử Dụng Đất</h5>
      </div>
      <div class="card-body">
        <div class="row g-3">
          @if($asset->land_origin_source)
          <div class="col-md-12">
            <label class="form-label fw-medium">Nguồn Gốc Sử Dụng Đất:</label>
            <p class="mb-0">{{ $asset->land_origin_source }}</p>
          </div>
          @endif
          @if($asset->transfer_recognition)
          <div class="col-md-12">
            <label class="form-label fw-medium">Nhận Chuyển Nhượng Đất Được Công Nhận QSDĐ:</label>
            <p class="mb-0">{{ $asset->transfer_recognition }}</p>
          </div>
          @endif
          @if($asset->owner_info)
          <div class="col-md-6">
            <label class="form-label fw-medium">Thông Tin Gia Chủ, Thửa Đất:</label>
            <p class="mb-0">{{ $asset->owner_info }}</p>
          </div>
          @endif
          @if($asset->map_sheet_number)
          <div class="col-md-6">
            <label class="form-label fw-medium">Tờ Bản Đồ Số:</label>
            <p class="mb-0">{{ $asset->map_sheet_number }}</p>
          </div>
          @endif
        </div>
      </div>
    </div>

    <!-- Address Information -->
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="card-title mb-0">Thông Tin Địa Chỉ</h5>
      </div>
      <div class="card-body">
        <div class="row g-3">
          <div class="col-md-12">
            <label class="form-label fw-medium">Địa Chỉ Đầy Đủ:</label>
            <p class="mb-0">{{ $asset->full_address ?: 'Chưa có thông tin' }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Area and Usage Information -->
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="card-title mb-0">Thông Tin Diện Tích & Sử Dụng</h5>
      </div>
      <div class="card-body">
        <div class="row g-3">
          @if($asset->total_area)
          <div class="col-md-4">
            <label class="form-label fw-medium">Tổng Diện Tích:</label>
            <p class="mb-0">{{ number_format($asset->total_area, 2) }} m²</p>
          </div>
          @endif
          @if($asset->residential_area)
          <div class="col-md-4">
            <label class="form-label fw-medium">Diện Tích Đất Ở:</label>
            <p class="mb-0">{{ number_format($asset->residential_area, 2) }} m²</p>
          </div>
          @endif
          @if($asset->agricultural_area)
          <div class="col-md-4">
            <label class="form-label fw-medium">Diện Tích Đất Nông Nghiệp:</label>
            <p class="mb-0">{{ number_format($asset->agricultural_area, 2) }} m²</p>
          </div>
          @endif
          @if($asset->usage_form)
          <div class="col-md-6">
            <label class="form-label fw-medium">Hình Thức Sử Dụng:</label>
            <p class="mb-0">{{ $asset->usage_form }}</p>
          </div>
          @endif
          @if($asset->usage_purpose)
          <div class="col-md-6">
            <label class="form-label fw-medium">Mục Đích Sử Dụng:</label>
            <p class="mb-0">{{ $asset->usage_purpose }}</p>
          </div>
          @endif
          @if($asset->usage_term)
          <div class="col-md-12">
            <label class="form-label fw-medium">Thời Hạn Sử Dụng Đất:</label>
            <p class="mb-0">{{ $asset->usage_term }}</p>
          </div>
          @endif
        </div>
      </div>
    </div>

    <!-- Files -->
    @if($asset->documents || $asset->images)
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="card-title mb-0">Tài Liệu & Hình Ảnh</h5>
      </div>
      <div class="card-body">
        @if($asset->documents)
        <div class="mb-3">
          <label class="form-label fw-medium">Tài Liệu:</label>
          <div class="list-group">
            @foreach($asset->documents as $document)
            <a href="{{ Storage::url($document['path']) }}" target="_blank" class="list-group-item list-group-item-action">
              <i class="ri-file-line me-2"></i>{{ $document['name'] }}
              <small class="text-muted">({{ number_format($document['size'] / 1024, 2) }} KB)</small>
            </a>
            @endforeach
          </div>
        </div>
        @endif

        @if($asset->images)
        <div>
          <label class="form-label fw-medium">Hình Ảnh:</label>
          <div class="row g-2">
            @foreach($asset->images as $image)
            <div class="col-md-3">
              <a href="{{ Storage::url($image['path']) }}" target="_blank">
                <img src="{{ Storage::url($image['path']) }}" class="img-fluid rounded" alt="{{ $image['name'] }}">
              </a>
            </div>
            @endforeach
          </div>
        </div>
        @endif
      </div>
    </div>
    @endif
  </div>

  <div class="col-md-4">
    <!-- Status and Metadata -->
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="card-title mb-0">Trạng Thái & Thông Tin</h5>
      </div>
      <div class="card-body">
        <div class="row g-3">
          <div class="col-md-12">
            <label class="form-label fw-medium">Trạng Thái:</label>
            <p class="mb-0">
              <span class="badge {{ $asset->status_badge_class }}">{{ $asset->status_label }}</span>
            </p>
          </div>
          @if($asset->estimated_value)
          <div class="col-md-12">
            <label class="form-label fw-medium">Giá Trị Ước Tính:</label>
            <p class="mb-0 text-success fw-medium">{{ $asset->formatted_estimated_value }}</p>
          </div>
          @endif
          @if($asset->acquisition_date)
          <div class="col-md-12">
            <label class="form-label fw-medium">Ngày Tiếp Nhận:</label>
            <p class="mb-0">{{ $asset->acquisition_date->format('d/m/Y') }}</p>
          </div>
          @endif
          <div class="col-md-12">
            <label class="form-label fw-medium">Ngày Tạo:</label>
            <p class="mb-0">{{ $asset->created_at->format('d/m/Y H:i') }}</p>
          </div>
          <div class="col-md-12">
            <label class="form-label fw-medium">Cập Nhật Lần Cuối:</label>
            <p class="mb-0">{{ $asset->updated_at->format('d/m/Y H:i') }}</p>
          </div>
          @if($asset->notes)
          <div class="col-md-12">
            <label class="form-label fw-medium">Ghi Chú:</label>
            <p class="mb-0">{{ $asset->notes }}</p>
          </div>
          @endif
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="card">
      <div class="card-header">
        <h5 class="card-title mb-0">Thao Tác Nhanh</h5>
      </div>
      <div class="card-body">
        <div class="d-grid gap-2">
          <a href="{{ route('admin.assets.edit', $asset->id) }}" class="btn btn-primary">
            <i class="ri-edit-line me-1"></i>Chỉnh Sửa
          </a>
          <button type="button" class="btn btn-danger delete-asset-btn" 
                  data-id="{{ $asset->id }}" 
                  data-name="{{ $asset->name }}">
            <i class="ri-delete-bin-line me-1"></i>Xóa Tài Sản
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
@endsection
