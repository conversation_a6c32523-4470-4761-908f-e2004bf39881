@extends('layouts/layoutMaster')

@section('title', 'Chỉnh Sửa Tài <PERSON>ản')

@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss',
  'resources/css/assets.css'
])
@endsection

@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
])
@endsection

@section('page-script')
@vite([
  'resources/js/asset-form.js'
])
@endsection

@section('content')
{{-- Flash Messages --}}
@if(session('success'))
<div class="alert alert-success alert-dismissible fade show" role="alert">
  <i class="ri-check-line me-2"></i>{{ session('success') }}
  <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
@endif

@if(session('error'))
<div class="alert alert-danger alert-dismissible fade show" role="alert">
  <i class="ri-error-warning-line me-2"></i>{{ session('error') }}
  <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
@endif

<div class="d-flex justify-content-between align-items-center mb-4">
  <div>
    <h4 class="mb-1">Chỉnh Sửa Tài Sản</h4>
    <p class="mb-0">Cập nhật thông tin tài sản: <strong>{{ $asset->name }}</strong></p>
  </div>
  <div class="d-flex gap-2">
    <a href="{{ route('admin.assets.show', $asset->id) }}" class="btn btn-outline-info">
      <i class="ri-eye-line me-1"></i>Xem Chi Tiết
    </a>
    <a href="{{ route('admin.assets.index') }}" class="btn btn-outline-secondary">
      <i class="ri-arrow-left-line me-1"></i>Quay Lại
    </a>
  </div>
</div>

<form action="{{ route('admin.assets.update', $asset->id) }}" method="POST" enctype="multipart/form-data">
  @csrf
  @method('PUT')
  @include('admin.assets.partials.form', ['isEdit' => true])

  <div class="d-flex justify-content-end gap-3 mt-4">
    <a href="{{ route('admin.assets.index') }}" class="btn btn-outline-secondary">
      <i class="ri-close-line me-1"></i>Hủy
    </a>
    <button type="submit" class="btn btn-primary">
      <i class="ri-save-line me-1"></i>Cập Nhật
    </button>
  </div>
</form>
@endsection
