/**
 * Party Form JavaScript
 */

'use strict';

$(function () {
  // Initialize Select2
  if ($('.select2').length) {
    $('.select2').select2({
      placeholder: 'Chọn...',
      allowClear: true
    });
  }

  // Initialize Flatpickr for date inputs
  if ($('.flatpickr-date').length) {
    $('.flatpickr-date').flatpickr({
      dateFormat: 'd/m/Y',
      locale: 'vn',
      allowInput: true,
      maxDate: 'today'
    });
  }

  // Handle marital status change
  $('#marital_status').on('change', function() {
    const maritalStatus = $(this).val();
    const spouseInfo = $('#spouseInfo');
    const marriageCertificateSection = $('#marriageCertificateSection');
    const spouseFields = spouseInfo.find('input, textarea');
    const marriageFields = marriageCertificateSection.find('input');

    if (maritalStatus === 'married') {
      spouseInfo.show();
      marriageCertificateSection.show();
      // Make spouse fields required
      $('#spouse_name, #spouse_date_of_birth').attr('required', true);
      // Make marriage certificate fields required
      $('#marriage_certificate_number, #marriage_certificate_date, #marriage_certificate_place').attr('required', true);
    } else {
      spouseInfo.hide();
      marriageCertificateSection.hide();
      // Remove required attribute and clear values
      spouseFields.removeAttr('required').val('');
      marriageFields.removeAttr('required').val('');
      // Clear validation errors
      spouseFields.removeClass('is-invalid').next('.invalid-feedback').remove();
      marriageFields.removeClass('is-invalid').next('.invalid-feedback').remove();
    }
  });

  // Trigger marital status change on page load to show/hide spouse info
  $('#marital_status').trigger('change');

  // Form validation
  $('#partyForm').on('submit', function(e) {
    let isValid = true;
    const form = $(this);

    // Clear previous validation errors
    form.find('.is-invalid').removeClass('is-invalid');
    form.find('.invalid-feedback').remove();

    // Validate required fields
    form.find('input[required], select[required], textarea[required]').each(function() {
      const field = $(this);
      const value = field.val();

      if (!value || value.trim() === '') {
        isValid = false;
        field.addClass('is-invalid');

        const label = field.closest('.form-floating').find('label').text().replace('*', '').trim();
        const errorMsg = `${label} là bắt buộc.`;

        field.after(`<div class="invalid-feedback">${errorMsg}</div>`);
      }
    });

    // Validate email format
    const emailField = $('#email');
    if (emailField.val()) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(emailField.val())) {
        isValid = false;
        emailField.addClass('is-invalid');
        emailField.after('<div class="invalid-feedback">Email không đúng định dạng.</div>');
      }
    }

    // Validate phone number format
    const phoneField = $('#phone_number');
    if (phoneField.val()) {
      const phoneRegex = /^[0-9+\-\s()]+$/;
      if (!phoneRegex.test(phoneField.val())) {
        isValid = false;
        phoneField.addClass('is-invalid');
        phoneField.after('<div class="invalid-feedback">Số điện thoại không đúng định dạng.</div>');
      }
    }

    // Validate date fields
    $('.flatpickr-date').each(function() {
      const field = $(this);
      const value = field.val();

      if (value) {
        const dateRegex = /^\d{2}\/\d{2}\/\d{4}$/;
        if (!dateRegex.test(value)) {
          isValid = false;
          field.addClass('is-invalid');
          field.after('<div class="invalid-feedback">Ngày không đúng định dạng dd/mm/yyyy.</div>');
        } else {
          // Check if date is valid
          const parts = value.split('/');
          const day = parseInt(parts[0], 10);
          const month = parseInt(parts[1], 10);
          const year = parseInt(parts[2], 10);
          const date = new Date(year, month - 1, day);

          if (date.getDate() !== day || date.getMonth() !== month - 1 || date.getFullYear() !== year) {
            isValid = false;
            field.addClass('is-invalid');
            field.after('<div class="invalid-feedback">Ngày không hợp lệ.</div>');
          } else if (date > new Date()) {
            isValid = false;
            field.addClass('is-invalid');
            field.after('<div class="invalid-feedback">Ngày không được sau ngày hôm nay.</div>');
          }
        }
      }
    });

    // Validate ID number uniqueness (only for create form)
    const idNumberField = $('#id_number');
    if (idNumberField.val() && window.location.pathname.includes('/create')) {
      // This would typically be done server-side, but we can add client-side check
      // For now, we'll rely on server-side validation
    }

    if (!isValid) {
      e.preventDefault();

      // Scroll to first error
      const firstError = form.find('.is-invalid').first();
      if (firstError.length) {
        $('html, body').animate({
          scrollTop: firstError.offset().top - 100
        }, 500);
        firstError.focus();
      }

      // Show error message
      Swal.fire({
        title: 'Lỗi!',
        text: 'Vui lòng kiểm tra lại thông tin đã nhập.',
        icon: 'error',
        customClass: {
          confirmButton: 'btn btn-danger'
        }
      });
    }
  });

  // Auto-format phone number
  $('#phone_number').on('input', function() {
    let value = $(this).val().replace(/\D/g, ''); // Remove non-digits

    // Format Vietnamese phone numbers
    if (value.length > 0) {
      if (value.startsWith('84')) {
        // International format
        if (value.length > 2) {
          value = '+84 ' + value.substring(2);
        }
      } else if (value.startsWith('0')) {
        // Domestic format
        if (value.length > 3) {
          value = value.substring(0, 4) + ' ' + value.substring(4);
        }
        if (value.length > 8) {
          value = value.substring(0, 8) + ' ' + value.substring(8);
        }
      }
    }

    $(this).val(value);
  });

  // Auto-format ID number
  $('#id_number, #spouse_id_number').on('input', function() {
    let value = $(this).val().replace(/\D/g, ''); // Remove non-digits

    // Limit to 12 digits for CCCD or 9 digits for CMND
    if (value.length > 12) {
      value = value.substring(0, 12);
    }

    $(this).val(value);
  });

  // Copy permanent address to temporary address
  $('#permanent_address').on('blur', function() {
    const tempAddress = $('#temporary_address');
    if (!tempAddress.val() && $(this).val()) {
      Swal.fire({
        title: 'Sao chép địa chỉ',
        text: 'Bạn có muốn sao chép địa chỉ thường trú sang địa chỉ tạm trú?',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'Có',
        cancelButtonText: 'Không',
        customClass: {
          confirmButton: 'btn btn-primary',
          cancelButton: 'btn btn-outline-secondary'
        }
      }).then((result) => {
        if (result.isConfirmed) {
          tempAddress.val($(this).val());
        }
      });
    }
  });

  // Character counter for textarea fields
  $('textarea').each(function() {
    const textarea = $(this);
    const maxLength = textarea.attr('maxlength');

    if (maxLength) {
      const counter = $(`<small class="text-muted float-end">${textarea.val().length}/${maxLength}</small>`);
      textarea.after(counter);

      textarea.on('input', function() {
        counter.text(`${$(this).val().length}/${maxLength}`);

        if ($(this).val().length > maxLength * 0.9) {
          counter.removeClass('text-muted').addClass('text-warning');
        } else {
          counter.removeClass('text-warning').addClass('text-muted');
        }
      });
    }
  });

  // Auto-save draft (optional feature)
  let autoSaveTimer;
  const formFields = $('#partyForm input, #partyForm select, #partyForm textarea');

  formFields.on('input change', function() {
    clearTimeout(autoSaveTimer);
    autoSaveTimer = setTimeout(function() {
      // Save form data to localStorage
      const formData = {};
      formFields.each(function() {
        const field = $(this);
        formData[field.attr('name')] = field.val();
      });

      localStorage.setItem('party_form_draft', JSON.stringify(formData));
    }, 2000); // Save after 2 seconds of inactivity
  });

  // Load draft on page load (for create form only)
  if (window.location.pathname.includes('/create')) {
    const draft = localStorage.getItem('party_form_draft');
    if (draft) {
      try {
        const formData = JSON.parse(draft);

        Swal.fire({
          title: 'Khôi phục dữ liệu',
          text: 'Có dữ liệu đã lưu trước đó. Bạn có muốn khôi phục?',
          icon: 'question',
          showCancelButton: true,
          confirmButtonText: 'Khôi phục',
          cancelButtonText: 'Bỏ qua',
          customClass: {
            confirmButton: 'btn btn-primary',
            cancelButton: 'btn btn-outline-secondary'
          }
        }).then((result) => {
          if (result.isConfirmed) {
            Object.keys(formData).forEach(function(name) {
              const field = $(`[name="${name}"]`);
              if (field.length && formData[name]) {
                field.val(formData[name]).trigger('change');
              }
            });
          } else {
            localStorage.removeItem('party_form_draft');
          }
        });
      } catch (e) {
        localStorage.removeItem('party_form_draft');
      }
    }
  }

  // Clear draft when form is successfully submitted
  $('#partyForm').on('submit', function() {
    localStorage.removeItem('party_form_draft');
  });
});
