/**
 * Notary Office Management JavaScript
 * Handles DataTables, filtering, and CRUD operations for notary offices
 */

'use strict';

$(function () {
  let dt_notary_offices_table = $('.datatables-notary-offices');
  let notaryOfficesTable;

  // Initialize DataTable
  if (dt_notary_offices_table.length) {
    notaryOfficesTable = dt_notary_offices_table.DataTable({
      processing: true,
      serverSide: true,
      ajax: {
        url: '/admin/notary-offices-data',
        type: 'GET',
        data: function (d) {
          d.search = $('#searchInput').val();
          d.status = $('#statusFilter').val();
        }
      },
      columns: [
        { data: 'id', name: 'id', title: 'ID', width: '5%' },
        { data: 'name', name: 'name', title: 'Tên <PERSON>', width: '20%' },
        { data: 'code', name: 'code', title: 'Mã Số', width: '10%' },
        { data: 'address', name: 'address', title: 'Địa Chỉ', width: '25%' },
        { data: 'phone', name: 'phone', title: '<PERSON><PERSON><PERSON>n <PERSON>', width: '10%' },
        { data: 'email', name: 'email', title: 'Email', width: '15%' },
        { data: 'status_badge', name: 'status', title: 'Trạng Thái', width: '8%', orderable: false },
        { data: 'created_at', name: 'created_at', title: 'Ngày Tạo', width: '10%' },
        { data: 'actions', name: 'actions', title: 'Thao Tác', width: '12%', orderable: false, searchable: false }
      ],
      order: [[1, 'asc']],
      dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6 d-flex justify-content-center justify-content-md-end"f>>t<"row"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>',
      language: {
        lengthMenu: 'Hiển thị _MENU_ mục',
        search: 'Tìm kiếm:',
        searchPlaceholder: 'Tìm kiếm...',
        info: 'Hiển thị _START_ đến _END_ của _TOTAL_ mục',
        infoEmpty: 'Hiển thị 0 đến 0 của 0 mục',
        infoFiltered: '(lọc từ _MAX_ tổng số mục)',
        paginate: {
          first: 'Đầu',
          last: 'Cuối',
          next: 'Tiếp',
          previous: 'Trước'
        },
        emptyTable: 'Không có dữ liệu',
        zeroRecords: 'Không tìm thấy kết quả phù hợp'
      },
      responsive: {
        details: {
          display: $.fn.dataTable.Responsive.display.modal({
            header: function (row) {
              var data = row.data();
              return 'Chi tiết: ' + data['name'];
            }
          }),
          type: 'column',
          renderer: function (api, rowIdx, columns) {
            var data = $.map(columns, function (col, i) {
              return col.title !== ''
                ? '<tr data-dt-row="' +
                    col.rowIndex +
                    '" data-dt-column="' +
                    col.columnIndex +
                    '">' +
                    '<td>' +
                    col.title +
                    ':</td> ' +
                    '<td>' +
                    col.data +
                    '</td>' +
                    '</tr>'
                : '';
            }).join('');

            return data ? $('<table class="table"/><tbody />').append(data) : false;
          }
        }
      }
    });
  }

  // Filter functionality
  $('#filterBtn').on('click', function () {
    notaryOfficesTable.ajax.reload();
  });

  $('#resetBtn').on('click', function () {
    $('#searchInput').val('');
    $('#statusFilter').val('');
    notaryOfficesTable.ajax.reload();
  });

  // Enter key search
  $('#searchInput').on('keypress', function (e) {
    if (e.which === 13) {
      notaryOfficesTable.ajax.reload();
    }
  });

  // Delete functionality
  $(document).on('click', '.delete-record', function () {
    const id = $(this).data('id');
    const name = $(this).data('name');

    Swal.fire({
      title: 'Xác nhận xóa',
      text: `Bạn có chắc chắn muốn xóa phòng công chứng "${name}"?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Xóa',
      cancelButtonText: 'Hủy bỏ',
      confirmButtonColor: '#d33',
      cancelButtonColor: '#6c757d'
    }).then((result) => {
      if (result.isConfirmed) {
        $.ajax({
          url: `/admin/notary-offices/${id}`,
          type: 'DELETE',
          headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
          },
          success: function (response) {
            if (response.success) {
              Swal.fire({
                icon: 'success',
                title: 'Đã xóa!',
                text: response.message,
                timer: 3000,
                showConfirmButton: false
              });
              notaryOfficesTable.ajax.reload();
            } else {
              Swal.fire({
                icon: 'error',
                title: 'Lỗi!',
                text: response.message
              });
            }
          },
          error: function (xhr) {
            let message = 'Có lỗi xảy ra khi xóa phòng công chứng.';
            if (xhr.responseJSON && xhr.responseJSON.message) {
              message = xhr.responseJSON.message;
            }
            Swal.fire({
              icon: 'error',
              title: 'Lỗi!',
              text: message
            });
          }
        });
      }
    });
  });

  // Toggle status functionality
  $(document).on('click', '.toggle-status', function () {
    const id = $(this).data('id');
    const currentStatus = $(this).data('status');
    const newStatusText = currentStatus === 'active' ? 'tạm ngưng' : 'kích hoạt';

    Swal.fire({
      title: 'Xác nhận thay đổi trạng thái',
      text: `Bạn có chắc chắn muốn ${newStatusText} phòng công chứng này?`,
      icon: 'question',
      showCancelButton: true,
      confirmButtonText: 'Xác nhận',
      cancelButtonText: 'Hủy bỏ'
    }).then((result) => {
      if (result.isConfirmed) {
        $.ajax({
          url: `/admin/notary-offices/${id}/toggle-status`,
          type: 'POST',
          headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
          },
          success: function (response) {
            if (response.success) {
              Swal.fire({
                icon: 'success',
                title: 'Thành công!',
                text: response.message,
                timer: 3000,
                showConfirmButton: false
              });
              notaryOfficesTable.ajax.reload();
            } else {
              Swal.fire({
                icon: 'error',
                title: 'Lỗi!',
                text: response.message
              });
            }
          },
          error: function (xhr) {
            let message = 'Có lỗi xảy ra khi thay đổi trạng thái.';
            if (xhr.responseJSON && xhr.responseJSON.message) {
              message = xhr.responseJSON.message;
            }
            Swal.fire({
              icon: 'error',
              title: 'Lỗi!',
              text: message
            });
          }
        });
      }
    });
  });

  // Auto-refresh every 30 seconds
  setInterval(function () {
    if (notaryOfficesTable) {
      notaryOfficesTable.ajax.reload(null, false);
    }
  }, 30000);
});
