/**
 * Notary Office Form JavaScript
 * Handles form validation, image preview, and form submission
 */

'use strict';

$(function () {
  const notaryOfficeForm = $('#notaryOfficeForm');

  // Form validation
  if (notaryOfficeForm.length) {
    const fv = FormValidation.formValidation(notaryOfficeForm[0], {
      fields: {
        name: {
          validators: {
            notEmpty: {
              message: 'Vui lòng nhập tên phòng công chứng'
            },
            stringLength: {
              max: 255,
              message: 'Tên phòng công chứng không được vượt quá 255 ký tự'
            }
          }
        },
        code: {
          validators: {
            notEmpty: {
              message: 'Vui lòng nhập mã số phòng công chứng'
            },
            stringLength: {
              max: 50,
              message: 'M<PERSON> số không được vượt quá 50 ký tự'
            }
          }
        },
        address: {
          validators: {
            notEmpty: {
              message: '<PERSON><PERSON> lòng nhập địa chỉ'
            }
          }
        },
        phone: {
          validators: {
            stringLength: {
              max: 20,
              message: '<PERSON><PERSON> điện thoại không được vượt quá 20 ký tự'
            },
            regexp: {
              regexp: /^[0-9+\-\s()]*$/,
              message: 'Số điện thoại không đúng định dạng'
            }
          }
        },
        email: {
          validators: {
            emailAddress: {
              message: 'Email không đúng định dạng'
            },
            stringLength: {
              max: 255,
              message: 'Email không được vượt quá 255 ký tự'
            }
          }
        },
        website: {
          validators: {
            uri: {
              message: 'Website không đúng định dạng'
            },
            stringLength: {
              max: 255,
              message: 'Website không được vượt quá 255 ký tự'
            }
          }
        },
        status: {
          validators: {
            notEmpty: {
              message: 'Vui lòng chọn trạng thái'
            }
          }
        },
        sort_order: {
          validators: {
            integer: {
              min: 0,
              message: 'Thứ tự sắp xếp phải là số nguyên không âm'
            }
          }
        }
      },
      plugins: {
        trigger: new FormValidation.plugins.Trigger(),
        bootstrap5: new FormValidation.plugins.Bootstrap5({
          eleValidClass: '',
          rowSelector: function (field, ele) {
            return '.mb-3, .mb-4';
          }
        }),
        submitButton: new FormValidation.plugins.SubmitButton(),
        autoFocus: new FormValidation.plugins.AutoFocus()
      }
    });

    // Handle form submission
    fv.on('core.form.valid', function () {
      // Show loading state
      const submitBtn = notaryOfficeForm.find('button[type="submit"]');
      const originalText = submitBtn.html();
      submitBtn.html('<i class="ri-loader-4-line ri-spin me-1"></i>Đang xử lý...').prop('disabled', true);

      // Submit form
      notaryOfficeForm[0].submit();
    });
  }

  // Image preview functionality
  $('#image').on('change', function () {
    const file = this.files[0];
    const preview = $('#imagePreview');
    const previewImg = $('#previewImg');

    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif'];
      if (!allowedTypes.includes(file.type)) {
        Swal.fire({
          icon: 'error',
          title: 'Lỗi!',
          text: 'Vui lòng chọn file ảnh (JPG, PNG, GIF)'
        });
        $(this).val('');
        preview.hide();
        return;
      }

      // Validate file size (2MB)
      if (file.size > 2 * 1024 * 1024) {
        Swal.fire({
          icon: 'error',
          title: 'Lỗi!',
          text: 'Kích thước file không được vượt quá 2MB'
        });
        $(this).val('');
        preview.hide();
        return;
      }

      // Show preview
      const reader = new FileReader();
      reader.onload = function (e) {
        previewImg.attr('src', e.target.result);
        preview.show();
      };
      reader.readAsDataURL(file);
    } else {
      preview.hide();
    }
  });

  // Remove image checkbox functionality
  $('#remove_image').on('change', function () {
    if ($(this).is(':checked')) {
      $('.current-image').addClass('opacity-50');
      $('#image').prop('required', false);
    } else {
      $('.current-image').removeClass('opacity-50');
    }
  });

  // Auto-generate code from name
  $('#name').on('input', function () {
    const name = $(this).val();
    const codeField = $('#code');
    
    // Only auto-generate if code field is empty
    if (!codeField.val()) {
      let code = name
        .toUpperCase()
        .replace(/[^A-Z0-9\s]/g, '') // Remove special characters
        .replace(/\s+/g, '') // Remove spaces
        .substring(0, 10); // Limit to 10 characters
      
      codeField.val(code);
    }
  });

  // Format phone number
  $('#phone').on('input', function () {
    let value = $(this).val().replace(/\D/g, ''); // Remove non-digits
    
    // Format as Vietnamese phone number
    if (value.length > 0) {
      if (value.startsWith('84')) {
        // International format
        value = '+84 ' + value.substring(2);
      } else if (value.startsWith('0')) {
        // Domestic format
        if (value.length >= 4) {
          value = value.substring(0, 4) + ' ' + value.substring(4);
        }
        if (value.length >= 9) {
          value = value.substring(0, 9) + ' ' + value.substring(9);
        }
      }
    }
    
    $(this).val(value);
  });

  // Website URL formatting
  $('#website').on('blur', function () {
    let url = $(this).val().trim();
    if (url && !url.match(/^https?:\/\//)) {
      $(this).val('https://' + url);
    }
  });

  // Character counter for description
  $('#description').on('input', function () {
    const maxLength = 1000;
    const currentLength = $(this).val().length;
    const remaining = maxLength - currentLength;
    
    let counterElement = $(this).siblings('.char-counter');
    if (counterElement.length === 0) {
      counterElement = $('<div class="char-counter form-text"></div>');
      $(this).after(counterElement);
    }
    
    counterElement.text(`${currentLength}/${maxLength} ký tự`);
    
    if (remaining < 50) {
      counterElement.addClass('text-warning');
    } else {
      counterElement.removeClass('text-warning');
    }
    
    if (remaining < 0) {
      counterElement.addClass('text-danger').removeClass('text-warning');
    } else {
      counterElement.removeClass('text-danger');
    }
  });

  // Initialize character counter
  $('#description').trigger('input');

  // Prevent form submission on Enter key (except in textarea)
  notaryOfficeForm.on('keypress', function (e) {
    if (e.which === 13 && e.target.tagName !== 'TEXTAREA') {
      e.preventDefault();
      return false;
    }
  });

  // Confirm navigation away from unsaved form
  let formChanged = false;
  notaryOfficeForm.on('change input', function () {
    formChanged = true;
  });

  $(window).on('beforeunload', function () {
    if (formChanged) {
      return 'Bạn có thay đổi chưa được lưu. Bạn có chắc chắn muốn rời khỏi trang này?';
    }
  });

  notaryOfficeForm.on('submit', function () {
    formChanged = false;
  });
});
