/**
 * Party Management JavaScript
 */

'use strict';

$(function () {
  let borderColor, bodyBg, headingColor;

  if (isDarkStyle) {
    borderColor = config.colors_dark.borderColor;
    bodyBg = config.colors_dark.bodyBg;
    headingColor = config.colors_dark.headingColor;
  } else {
    borderColor = config.colors.borderColor;
    bodyBg = config.colors.bodyBg;
    headingColor = config.colors.headingColor;
  }

  // Variable declaration for table
  var dt_table = $('.datatables-parties');

  // Parties datatable
  if (dt_table.length) {
    // Get default configuration
    var defaultConfig = DataTablesConfig.getDefaultConfig(isDarkStyle, config);

    var dt = dt_table.DataTable($.extend(true, {}, defaultConfig, {
      ajax: {
        url: baseUrl + 'admin/parties-data',
        type: 'GET',
        data: function(d) {
          // Add custom filters
          d.status_filter = $('#StatusFilter').val();
          d.role_filter = $('#RoleFilter').val();
          d.marital_status_filter = $('#MaritalStatusFilter').val();
        }
      },
      columns: [
        // columns according to JSON
        { data: '' },
        { data: 'id' },
        { data: 'full_name' },
        { data: 'id_number' },
        { data: 'phone_number' },
        { data: 'age' },
        { data: 'role_badge' },
        { data: 'marital_status_label' },
        { data: 'occupation' },
        { data: 'status_badge' },
        { data: 'created_at' },
        { data: 'actions' }
      ],
      columnDefs: DataTablesConfig.getCommonColumnDefs().concat([
        {
          // For Responsive
          className: 'control',
          searchable: false,
          orderable: false,
          responsivePriority: 2,
          targets: 0,
          render: function (data, type, full, meta) {
            return '';
          }
        },
        {
          // ID
          targets: 1,
          searchable: false,
          visible: false
        },
        {
          // Full Name
          targets: 2,
          responsivePriority: 1,
          render: function (data, type, full, meta) {
            var $name = full['full_name'];
            var $email = full['email'];
            if ($email) {
              return '<div class="d-flex flex-column"><span class="fw-medium">' + $name + '</span><small class="text-muted">' + $email + '</small></div>';
            }
            return '<span class="fw-medium">' + $name + '</span>';
          }
        },
        {
          // ID Number
          targets: 3,
          render: function (data, type, full, meta) {
            return data || '<span class="text-muted">-</span>';
          }
        },
        {
          // Phone Number
          targets: 4,
          render: function (data, type, full, meta) {
            return data || '<span class="text-muted">-</span>';
          }
        },
        {
          // Age
          targets: 5,
          render: function (data, type, full, meta) {
            return data || '<span class="text-muted">-</span>';
          }
        },
        {
          // Role Badge
          targets: 6,
          render: function (data, type, full, meta) {
            return data;
          }
        },
        {
          // Marital Status
          targets: 7,
          render: function (data, type, full, meta) {
            return data;
          }
        },
        {
          // Occupation
          targets: 8,
          render: function (data, type, full, meta) {
            return data || '<span class="text-muted">-</span>';
          }
        },
        {
          // Status Badge
          targets: 9,
          render: function (data, type, full, meta) {
            return data;
          }
        },
        {
          // Created At
          targets: 10,
          render: function (data, type, full, meta) {
            return data;
          }
        },
        {
          // Actions
          targets: -1,
          title: 'Thao tác',
          searchable: false,
          orderable: false,
          render: function (data, type, full, meta) {
            return data;
          }
        }
      ]),
      order: [[1, 'desc']],
      dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6 d-flex justify-content-center justify-content-md-end"f>>t<"row"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>',
      displayLength: 10,
      lengthMenu: [10, 25, 50, 75, 100],
      language: {
        lengthMenu: 'Hiển thị _MENU_ mục',
        search: 'Tìm kiếm:',
        searchPlaceholder: 'Tìm kiếm...',
        info: 'Hiển thị _START_ đến _END_ của _TOTAL_ mục',
        infoEmpty: 'Hiển thị 0 đến 0 của 0 mục',
        infoFiltered: '(lọc từ _MAX_ mục)',
        emptyTable: 'Không có dữ liệu',
        zeroRecords: 'Không tìm thấy kết quả nào',
        paginate: {
          first: 'Đầu',
          last: 'Cuối',
          next: 'Tiếp',
          previous: 'Trước'
        }
      },
      responsive: {
        details: {
          display: $.fn.dataTable.Responsive.display.modal({
            header: function (row) {
              var data = row.data();
              return 'Chi tiết: ' + data['full_name'];
            }
          }),
          type: 'column',
          renderer: function (api, rowIdx, columns) {
            var data = $.map(columns, function (col, i) {
              return col.title !== '' // ? Do not show row in modal popup if title is blank (for check box)
                ? '<tr data-dt-row="' +
                    col.rowIndex +
                    '" data-dt-column="' +
                    col.columnIndex +
                    '">' +
                    '<td>' +
                    col.title +
                    ':' +
                    '</td> ' +
                    '<td>' +
                    col.data +
                    '</td>' +
                    '</tr>'
                : '';
            }).join('');

            return data ? $('<table class="table"/><tbody />').append(data) : false;
          }
        }
      }
    }));

    // Filter functionality
    $('#StatusFilter, #RoleFilter, #MaritalStatusFilter').on('change', function () {
      dt.draw();
    });

    // Clear filters
    $('#clearFilters').on('click', function () {
      $('#StatusFilter, #RoleFilter, #MaritalStatusFilter').val('').trigger('change');
      dt.draw();
    });
  }
});

// Global functions for party management
window.quickViewParty = function(partyId) {
  $.ajax({
    url: baseUrl + 'admin/parties/' + partyId + '/quick-view',
    type: 'GET',
    success: function(response) {
      if (response.success) {
        const party = response.data;
        let content = `
          <div class="row">
            <div class="col-md-6">
              <h6 class="mb-3"><i class="ri-user-line me-2"></i>Thông tin cá nhân</h6>
              <table class="table table-borderless table-sm">
                <tr><td class="fw-medium">Họ và tên:</td><td>${party.full_name}</td></tr>
                <tr><td class="fw-medium">Ngày sinh:</td><td>${party.date_of_birth || 'Chưa có thông tin'}</td></tr>
                <tr><td class="fw-medium">Giới tính:</td><td>${party.gender_label}</td></tr>
                <tr><td class="fw-medium">CCCD/CMND:</td><td>${party.id_number || 'Chưa có thông tin'}</td></tr>
                <tr><td class="fw-medium">Điện thoại:</td><td>${party.phone_number || 'Chưa có thông tin'}</td></tr>
                <tr><td class="fw-medium">Email:</td><td>${party.email || 'Chưa có thông tin'}</td></tr>
              </table>
            </div>
            <div class="col-md-6">
              <h6 class="mb-3"><i class="ri-briefcase-line me-2"></i>Nghề nghiệp & Vai trò</h6>
              <table class="table table-borderless table-sm">
                <tr><td class="fw-medium">Nghề nghiệp:</td><td>${party.occupation || 'Chưa có thông tin'}</td></tr>
                <tr><td class="fw-medium">Nơi làm việc:</td><td>${party.workplace || 'Chưa có thông tin'}</td></tr>
                <tr><td class="fw-medium">Vai trò:</td><td>${party.role_in_case_label}</td></tr>
                <tr><td class="fw-medium">Tình trạng HN:</td><td>${party.marital_status_label}</td></tr>
                <tr><td class="fw-medium">Trạng thái:</td><td>${party.status_badge}</td></tr>
                <tr><td class="fw-medium">Ngày tạo:</td><td>${party.created_at}</td></tr>
              </table>
            </div>
          </div>
        `;

        if (party.notes) {
          content += `
            <div class="row mt-3">
              <div class="col-12">
                <h6 class="mb-2"><i class="ri-file-text-line me-2"></i>Ghi chú</h6>
                <p class="text-muted">${party.notes}</p>
              </div>
            </div>
          `;
        }

        $('#quickViewContent').html(content);
        $('#quickViewModal').modal('show');
      } else {
        Swal.fire({
          title: 'Lỗi!',
          text: response.message,
          icon: 'error',
          customClass: {
            confirmButton: 'btn btn-danger'
          }
        });
      }
    },
    error: function() {
      Swal.fire({
        title: 'Lỗi!',
        text: 'Có lỗi xảy ra khi tải thông tin đương sự.',
        icon: 'error',
        customClass: {
          confirmButton: 'btn btn-danger'
        }
      });
    }
  });
};

window.togglePartyStatus = function(partyId) {
  Swal.fire({
    title: 'Xác nhận thay đổi trạng thái',
    text: 'Bạn có chắc chắn muốn thay đổi trạng thái của đương sự này?',
    icon: 'question',
    showCancelButton: true,
    confirmButtonText: 'Đồng ý',
    cancelButtonText: 'Hủy',
    customClass: {
      confirmButton: 'btn btn-primary',
      cancelButton: 'btn btn-outline-secondary'
    }
  }).then((result) => {
    if (result.isConfirmed) {
      $.ajax({
        url: baseUrl + 'admin/parties/' + partyId + '/toggle-status',
        type: 'POST',
        data: {
          _token: $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
          if (response.success) {
            Swal.fire({
              title: 'Thành công!',
              text: response.message,
              icon: 'success',
              customClass: {
                confirmButton: 'btn btn-success'
              }
            });
            $('.datatables-parties').DataTable().ajax.reload();
          } else {
            Swal.fire({
              title: 'Lỗi!',
              text: response.message,
              icon: 'error',
              customClass: {
                confirmButton: 'btn btn-danger'
              }
            });
          }
        },
        error: function() {
          Swal.fire({
            title: 'Lỗi!',
            text: 'Có lỗi xảy ra khi thay đổi trạng thái.',
            icon: 'error',
            customClass: {
              confirmButton: 'btn btn-danger'
            }
          });
        }
      });
    }
  });
};

window.deleteParty = function(partyId) {
  Swal.fire({
    title: 'Xác nhận xóa',
    text: 'Bạn có chắc chắn muốn xóa đương sự này? Hành động này không thể hoàn tác!',
    icon: 'warning',
    showCancelButton: true,
    confirmButtonText: 'Xóa',
    cancelButtonText: 'Hủy',
    customClass: {
      confirmButton: 'btn btn-danger',
      cancelButton: 'btn btn-outline-secondary'
    }
  }).then((result) => {
    if (result.isConfirmed) {
      $.ajax({
        url: baseUrl + 'admin/parties/' + partyId,
        type: 'DELETE',
        data: {
          _token: $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
          if (response.success) {
            Swal.fire({
              title: 'Đã xóa!',
              text: response.message,
              icon: 'success',
              customClass: {
                confirmButton: 'btn btn-success'
              }
            });
            $('.datatables-parties').DataTable().ajax.reload();
          } else {
            Swal.fire({
              title: 'Lỗi!',
              text: response.message,
              icon: 'error',
              customClass: {
                confirmButton: 'btn btn-danger'
              }
            });
          }
        },
        error: function() {
          Swal.fire({
            title: 'Lỗi!',
            text: 'Có lỗi xảy ra khi xóa đương sự.',
            icon: 'error',
            customClass: {
              confirmButton: 'btn btn-danger'
            }
          });
        }
      });
    }
  });
};
