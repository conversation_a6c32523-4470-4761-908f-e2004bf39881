/**
 * Asset Type Form - Simple Version (Backup)
 * Use this if the main form validation has issues
 */

'use strict';

$(function () {
  console.log('Asset Type Form Simple - Loading...');
  
  const form = document.getElementById('assetTypeForm');
  
  if (!form) {
    console.log('Form not found');
    return;
  }
  
  console.log('Form found, setting up simple handlers...');
  
  // Simple form validation and submission
  form.addEventListener('submit', function(e) {
    console.log('Form submit event triggered');
    
    // Basic validation
    const nameField = form.querySelector('#name');
    const statusField = form.querySelector('#status');
    const sortOrderField = form.querySelector('#sort_order');
    
    let hasErrors = false;
    
    // Clear previous errors
    form.querySelectorAll('.is-invalid').forEach(field => {
      field.classList.remove('is-invalid');
    });
    
    // Validate name
    if (!nameField.value.trim()) {
      nameField.classList.add('is-invalid');
      hasErrors = true;
      console.log('Name validation failed');
    }
    
    // Validate status
    if (!statusField.value) {
      statusField.classList.add('is-invalid');
      hasErrors = true;
      console.log('Status validation failed');
    }
    
    // Validate sort_order if provided
    if (sortOrderField.value && (isNaN(sortOrderField.value) || parseInt(sortOrderField.value) < 0)) {
      sortOrderField.classList.add('is-invalid');
      hasErrors = true;
      console.log('Sort order validation failed');
    }
    
    if (hasErrors) {
      e.preventDefault();
      console.log('Form has errors, preventing submission');
      
      // Focus first error field
      const firstError = form.querySelector('.is-invalid');
      if (firstError) {
        firstError.focus();
        firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
      
      return false;
    }
    
    console.log('Form validation passed, allowing submission');
    
    // Show loading state
    const submitBtn = form.querySelector('button[type="submit"]');
    if (submitBtn) {
      const originalText = submitBtn.innerHTML;
      
      submitBtn.disabled = true;
      submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status"></span>Đang xử lý...';
      
      // Reset button state after a delay (in case of server errors)
      setTimeout(() => {
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
      }, 10000);
    }
    
    return true;
  });
  
  // Real-time preview functionality
  function updatePreview() {
    const name = $('#name').val();
    const icon = $('#icon').val();
    const status = $('#status').val();
    
    if (name || icon || status) {
      $('#previewCard').show();
      
      // Update name
      $('#namePreview').text(name || 'Tên loại tài sản');
      
      // Update icon
      let iconHtml = '<i class="ri-file-line ri-22px text-muted"></i>';
      if (icon) {
        if (icon.startsWith('ri-')) {
          iconHtml = '<i class="' + icon + ' ri-22px text-primary"></i>';
        } else {
          iconHtml = '<i class="ri-image-line ri-22px text-info"></i>';
        }
      }
      $('#iconPreview').html(iconHtml);
      
      // Update status
      const statusClass = status === 'active' ? 'success' : 'secondary';
      const statusText = status === 'active' ? 'Đang hoạt động' : 'Không hoạt động';
      $('#statusPreview').html('<span class="badge bg-label-' + statusClass + '">' + statusText + '</span>');
    } else {
      $('#previewCard').hide();
    }
  }

  // Bind preview update events
  $('#name, #icon, #status').on('input change', updatePreview);
  
  // Initial preview update
  updatePreview();

  // Icon input helper
  $('#icon').on('input', function() {
    const value = $(this).val();
    const helpText = $(this).siblings('.form-text');
    
    if (value.startsWith('ri-')) {
      helpText.html('RemixIcon class detected. <a href="https://remixicon.com/" target="_blank">Browse icons</a>');
    } else if (value) {
      helpText.html('File path detected. Make sure the file exists in storage.');
    } else {
      helpText.html('Nhập class RemixIcon (VD: ri-file-line) hoặc đường dẫn đến file icon');
    }
  });

  // Image preview functionality
  $('#image').on('change', function() {
    const file = this.files[0];
    const preview = $('#imagePreview');
    
    if (file) {
      const reader = new FileReader();
      reader.onload = function(e) {
        if (preview.length === 0) {
          $('#image').after('<div id="imagePreview" class="mt-2"><img src="' + e.target.result + '" class="img-thumbnail" style="max-width: 150px; max-height: 150px;"></div>');
        } else {
          preview.html('<img src="' + e.target.result + '" class="img-thumbnail" style="max-width: 150px; max-height: 150px;">');
        }
      };
      reader.readAsDataURL(file);
    } else {
      preview.remove();
    }
  });

  // Remove image checkbox handling
  $('#remove_image').on('change', function() {
    if ($(this).is(':checked')) {
      $('#image').val('').trigger('change');
      $(this).closest('.mt-3').find('img').css('opacity', '0.5');
    } else {
      $(this).closest('.mt-3').find('img').css('opacity', '1');
    }
  });

  // Character counter for description
  $('#description').on('input', function() {
    const maxLength = 1000;
    const currentLength = $(this).val().length;
    const remaining = maxLength - currentLength;
    
    let counterHtml = '<small class="text-muted">' + currentLength + '/' + maxLength + ' ký tự</small>';
    if (remaining < 100) {
      counterHtml = '<small class="text-warning">' + currentLength + '/' + maxLength + ' ký tự</small>';
    }
    if (remaining < 0) {
      counterHtml = '<small class="text-danger">' + currentLength + '/' + maxLength + ' ký tự</small>';
    }
    
    $(this).siblings('.form-text').html(counterHtml);
  });

  // Sort order input validation helper
  $('#sort_order').on('input', function() {
    const value = $(this).val();
    const helpText = $(this).siblings('.form-text');
    
    if (value === '' || value === '0') {
      helpText.html('Để trống hoặc 0 để tự động tạo thứ tự');
    } else if (parseInt(value) < 0) {
      helpText.html('<span class="text-danger">Thứ tự phải lớn hơn hoặc bằng 0</span>');
    } else if (parseInt(value) > 999999) {
      helpText.html('<span class="text-danger">Thứ tự không được vượt quá 999999</span>');
    } else {
      helpText.html('Số càng nhỏ sẽ hiển thị trước');
    }
  });

  // Prevent negative values in sort_order input
  $('#sort_order').on('keydown', function(e) {
    // Allow: backspace, delete, tab, escape, enter
    if ($.inArray(e.keyCode, [46, 8, 9, 27, 13]) !== -1 ||
        // Allow: Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
        (e.keyCode === 65 && e.ctrlKey === true) ||
        (e.keyCode === 67 && e.ctrlKey === true) ||
        (e.keyCode === 86 && e.ctrlKey === true) ||
        (e.keyCode === 88 && e.ctrlKey === true) ||
        // Allow: home, end, left, right
        (e.keyCode >= 35 && e.keyCode <= 39)) {
      return;
    }
    // Ensure that it is a number and stop the keypress
    if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105)) {
      e.preventDefault();
    }
  });
  
  console.log('Asset Type Form Simple - Initialized successfully');
});
