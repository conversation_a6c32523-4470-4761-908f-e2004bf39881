/**
 * Asset Management JavaScript
 */

'use strict';

$(function () {
  let borderColor, bodyBg, headingColor;

  if (isDarkStyle) {
    borderColor = config.colors_dark.borderColor;
    bodyBg = config.colors_dark.bodyBg;
    headingColor = config.colors_dark.headingColor;
  } else {
    borderColor = config.colors.borderColor;
    bodyBg = config.colors.bodyBg;
    headingColor = config.colors.headingColor;
  }

  // Variable declaration for table
  var dt_table = $('.datatables-assets');

  // Assets datatable
  if (dt_table.length) {
    // Get default configuration
    var defaultConfig = DataTablesConfig.getDefaultConfig(isDarkStyle, config);

    var dt = dt_table.DataTable($.extend(true, {}, defaultConfig, {
      ajax: {
        url: baseUrl + 'admin/assets-data',
        type: 'GET',
        data: function(d) {
          // Add custom filters
          d.status_filter = $('#statusFilter').val();
          d.asset_type_filter = $('#assetTypeFilter').val();
          d.province_filter = $('#provinceFilter').val();
        }
      },
      columns: [
        // columns according to JSON
        { data: '' },
        { data: 'asset_code' },
        { data: 'name' },
        { data: 'asset_type_name' },
        { data: 'full_address' },
        { data: 'total_area' },
        { data: 'estimated_value' },
        { data: 'status_badge' },
        { data: 'acquisition_date' },
        { data: 'created_at' },
        { data: 'actions' }
      ],
      columnDefs: DataTablesConfig.getCommonColumnDefs().concat([
        {
          // Asset Code column
          targets: 1,
          responsivePriority: 1,
          render: function (data, type, full, meta) {
            return '<span class="asset-code">' + data + '</span>';
          }
        },
        {
          // Name column
          targets: 2,
          responsivePriority: 2,
          render: function (data, type, full, meta) {
            return '<span class="fw-medium">' + data + '</span>';
          }
        },
        {
          // Asset Type column
          targets: 3,
          render: function (data, type, full, meta) {
            return data || '<span class="text-muted">-</span>';
          }
        },
        {
          // Address column
          targets: 4,
          render: function (data, type, full, meta) {
            return data || '<span class="text-muted">-</span>';
          }
        },
        {
          // Area column
          targets: 5,
          className: 'text-end',
          render: function (data, type, full, meta) {
            return data || '<span class="text-muted">-</span>';
          }
        },
        {
          // Value column
          targets: 6,
          className: 'text-end',
          render: function (data, type, full, meta) {
            return '<span class="value-display">' + data + '</span>';
          }
        },
        {
          // Status column
          targets: 7,
          className: 'text-center',
          render: function (data, type, full, meta) {
            return data;
          }
        },
        {
          // Acquisition Date column
          targets: 8,
          className: 'text-center',
          render: function (data, type, full, meta) {
            return data || '<span class="text-muted">-</span>';
          }
        },
        {
          // Created Date column
          targets: 9,
          className: 'text-center',
          render: function (data, type, full, meta) {
            return data;
          }
        },
        {
          // Actions column
          targets: -1,
          title: 'Thao tác',
          searchable: false,
          orderable: false,
          render: function (data, type, full, meta) {
            return data;
          }
        }
      ])
    }));

    // Apply filters
    $('#applyFilters').on('click', function() {
      dt.ajax.reload();
    });

    // Clear filters
    $('#clearFilters').on('click', function() {
      $('#statusFilter').val('').trigger('change');
      $('#assetTypeFilter').val('').trigger('change');
      $('#provinceFilter').val('');
      dt.ajax.reload();
    });

    // Select all checkbox
    $('#selectAll').on('change', function() {
      var isChecked = $(this).is(':checked');
      $('.asset-checkbox').prop('checked', isChecked);
      updateBulkActions();
    });

    // Individual checkboxes
    $(document).on('change', '.asset-checkbox', function() {
      updateBulkActions();

      // Update select all checkbox
      var totalCheckboxes = $('.asset-checkbox').length;
      var checkedCheckboxes = $('.asset-checkbox:checked').length;

      if (checkedCheckboxes === 0) {
        $('#selectAll').prop('indeterminate', false).prop('checked', false);
      } else if (checkedCheckboxes === totalCheckboxes) {
        $('#selectAll').prop('indeterminate', false).prop('checked', true);
      } else {
        $('#selectAll').prop('indeterminate', true);
      }
    });

    // Update bulk actions visibility
    function updateBulkActions() {
      var checkedCount = $('.asset-checkbox:checked').length;

      if (checkedCount > 0) {
        $('#bulkActions').removeClass('d-none');
        $('#selectedCount').text(checkedCount + ' tài sản được chọn');
      } else {
        $('#bulkActions').addClass('d-none');
      }
    }

    // Bulk delete
    $('#bulkDelete').on('click', function() {
      var selectedIds = [];
      $('.asset-checkbox:checked').each(function() {
        selectedIds.push($(this).val());
      });

      if (selectedIds.length === 0) {
        Swal.fire({
          title: 'Thông báo',
          text: 'Vui lòng chọn ít nhất một tài sản để xóa!',
          icon: 'warning',
          confirmButtonText: 'Đóng'
        });
        return;
      }

      Swal.fire({
        title: 'Xác nhận xóa',
        text: 'Bạn có chắc chắn muốn xóa ' + selectedIds.length + ' tài sản đã chọn?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Xóa',
        cancelButtonText: 'Hủy',
        customClass: {
          confirmButton: 'btn btn-danger me-3',
          cancelButton: 'btn btn-outline-secondary'
        },
        buttonsStyling: false
      }).then((result) => {
        if (result.isConfirmed) {
          $.ajax({
            url: baseUrl + 'admin/assets/bulk-delete',
            type: 'POST',
            data: {
              ids: selectedIds,
              _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
              if (response.success) {
                Swal.fire({
                  title: 'Thành công!',
                  text: response.message,
                  icon: 'success',
                  confirmButtonText: 'Đóng'
                });
                dt.ajax.reload();
                $('#bulkActions').addClass('d-none');
                $('#selectAll').prop('checked', false);
              } else {
                Swal.fire({
                  title: 'Lỗi!',
                  text: response.message,
                  icon: 'error',
                  confirmButtonText: 'Đóng'
                });
              }
            },
            error: function(xhr) {
              Swal.fire({
                title: 'Lỗi!',
                text: 'Có lỗi xảy ra khi xóa tài sản.',
                icon: 'error',
                confirmButtonText: 'Đóng'
              });
            }
          });
        }
      });
    });
  }

  // Initialize Select2 for filters
  $('#statusFilter, #assetTypeFilter').select2({
    placeholder: 'Chọn...',
    allowClear: true
  });

  // Delete Asset - Handle form submission with confirmation
  $(document).on('click', '.delete-asset-btn', function(e) {
    e.preventDefault();

    var form = $(this).closest('form');
    var assetName = form.data('name');

    Swal.fire({
      title: 'Xác nhận xóa',
      text: 'Bạn có chắc chắn muốn xóa tài sản "' + assetName + '"?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Xóa',
      cancelButtonText: 'Hủy',
      customClass: {
        confirmButton: 'btn btn-danger me-3',
        cancelButton: 'btn btn-outline-secondary'
      },
      buttonsStyling: false
    }).then((result) => {
      if (result.isConfirmed) {
        // Show loading state
        var submitBtn = $(this);
        var originalHtml = submitBtn.html();
        submitBtn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm"></span>');

        // Submit the form
        form.submit();
      }
    });
  });


});
