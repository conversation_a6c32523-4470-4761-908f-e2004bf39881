/**
 * Asset Form JavaScript
 */

'use strict';

$(function () {
  // Initialize Select2 for asset type
  $('#asset_type_id').select2({
    placeholder: 'Chọn loại tài sản...',
    allowClear: true
  });

  // Initialize Select2 for usage form
  $('#usage_form').select2({
    placeholder: '<PERSON><PERSON><PERSON> hình thức sử dụng...',
    allowClear: true
  });

  // Initialize Select2 for status
  $('#status').select2({
    placeholder: 'Chọn trạng thái...',
    allowClear: false
  });

  // Auto-calculate total area when residential or agricultural area changes
  $('#residential_area, #agricultural_area').on('input', function() {
    var residentialArea = parseFloat($('#residential_area').val()) || 0;
    var agriculturalArea = parseFloat($('#agricultural_area').val()) || 0;
    var totalArea = residentialArea + agriculturalArea;

    if (totalArea > 0) {
      $('#total_area').val(totalArea.toFixed(2));
    }
  });

  // Format number inputs
  $('#estimated_value').on('input', function() {
    var value = $(this).val().replace(/[^\d]/g, '');
    if (value) {
      $(this).val(parseInt(value).toLocaleString('vi-VN'));
    }
  });

  // File upload preview
  $('#document_files').on('change', function() {
    var files = this.files;
    var fileList = $('#documentFileList');

    if (!fileList.length) {
      $(this).after('<div id="documentFileList" class="mt-2"></div>');
      fileList = $('#documentFileList');
    }

    fileList.empty();

    if (files.length > 0) {
      var listHtml = '<small class="text-muted">Tài liệu đã chọn:</small><ul class="list-unstyled mt-1">';

      for (var i = 0; i < files.length; i++) {
        var file = files[i];
        var fileSize = (file.size / 1024 / 1024).toFixed(2);
        listHtml += '<li><i class="ri-file-line me-1"></i>' + file.name + ' (' + fileSize + ' MB)</li>';
      }

      listHtml += '</ul>';
      fileList.html(listHtml);
    }
  });

  $('#image_files').on('change', function() {
    var files = this.files;
    var previewContainer = $('#imagePreviewContainer');

    if (!previewContainer.length) {
      $(this).after('<div id="imagePreviewContainer" class="mt-2"></div>');
      previewContainer = $('#imagePreviewContainer');
    }

    previewContainer.empty();

    if (files.length > 0) {
      previewContainer.append('<small class="text-muted d-block mb-2">Hình ảnh đã chọn:</small>');
      var rowHtml = '<div class="row g-2">';

      for (var i = 0; i < files.length; i++) {
        var file = files[i];

        if (file.type.startsWith('image/')) {
          var reader = new FileReader();
          reader.onload = function(e) {
            rowHtml += '<div class="col-md-3"><img src="' + e.target.result + '" class="img-thumbnail" style="height: 100px; object-fit: cover;"></div>';

            if (i === files.length - 1) {
              rowHtml += '</div>';
              previewContainer.append(rowHtml);
            }
          };
          reader.readAsDataURL(file);
        }
      }
    }
  });

  // Form validation
  function validateForm(form) {
    var isValid = true;
    var errors = [];

    // Required fields
    var requiredFields = {
      'name': 'Tên tài sản',
      'status': 'Trạng thái'
    };

    $.each(requiredFields, function(field, label) {
      var value = form.find('[name="' + field + '"]').val();
      if (!value || value.trim() === '') {
        errors.push(label + ' là bắt buộc.');
        form.find('[name="' + field + '"]').addClass('is-invalid');
        isValid = false;
      } else {
        form.find('[name="' + field + '"]').removeClass('is-invalid');
      }
    });

    // Validate areas
    var totalArea = parseFloat(form.find('[name="total_area"]').val()) || 0;
    var residentialArea = parseFloat(form.find('[name="residential_area"]').val()) || 0;
    var agriculturalArea = parseFloat(form.find('[name="agricultural_area"]').val()) || 0;

    if ((residentialArea + agriculturalArea) > totalArea && totalArea > 0) {
      errors.push('Tổng diện tích đất ở và đất nông nghiệp không được vượt quá tổng diện tích.');
      form.find('[name="residential_area"], [name="agricultural_area"]').addClass('is-invalid');
      isValid = false;
    }

    // Validate estimated value
    var estimatedValue = form.find('[name="estimated_value"]').val();
    if (estimatedValue && estimatedValue.replace(/[^\d]/g, '') === '') {
      errors.push('Giá trị ước tính phải là số hợp lệ.');
      form.find('[name="estimated_value"]').addClass('is-invalid');
      isValid = false;
    }

    // Validate acquisition date
    var acquisitionDate = form.find('[name="acquisition_date"]').val();
    if (acquisitionDate) {
      var selectedDate = new Date(acquisitionDate);
      var today = new Date();
      today.setHours(23, 59, 59, 999); // End of today

      if (selectedDate > today) {
        errors.push('Ngày tiếp nhận không được là ngày tương lai.');
        form.find('[name="acquisition_date"]').addClass('is-invalid');
        isValid = false;
      }
    }

    return {
      isValid: isValid,
      errors: errors
    };
  }

  // Form submit handling (normal form submission)
  $('form').on('submit', function(e) {
    var form = $(this);
    var validation = validateForm(form);

    if (!validation.isValid) {
      e.preventDefault();
      Swal.fire({
        title: 'Lỗi validation!',
        html: validation.errors.join('<br>'),
        icon: 'error',
        confirmButtonText: 'Đóng'
      });
      return false;
    }

    // Convert estimated value back to number before submit
    var estimatedValue = form.find('[name="estimated_value"]').val();
    if (estimatedValue) {
      form.find('[name="estimated_value"]').val(estimatedValue.replace(/[^\d]/g, ''));
    }

    // Show loading state
    var submitBtn = form.find('button[type="submit"]');
    var originalText = submitBtn.html();
    submitBtn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-1"></span>Đang xử lý...');

    // Allow form to submit normally
    return true;
  });



  // Remove validation errors on input
  $('input, select, textarea').on('input change', function() {
    $(this).removeClass('is-invalid');
  });

  // Auto-generate asset code if empty
  $('#name').on('blur', function() {
    var assetCode = $('#asset_code').val();
    if (!assetCode) {
      // This would typically call an API to generate a unique code
      // For now, we'll let the server handle it
    }
  });
});
