/**
 * Notary Form JavaScript
 * Handles form validation, image preview, and form submission
 */

'use strict';

$(function () {
  const notaryForm = $('#notaryForm');

  // Initialize Select2 for notary office dropdown
  const notaryOfficeSelect = $('#notary_office_id');
  if (notaryOfficeSelect.length) {
    notaryOfficeSelect.select2({
      placeholder: 'Chọn phòng công chứng',
      allowClear: true,
      width: '100%'
    });
  }

  // Form validation
  if (notaryForm.length) {
    const fv = FormValidation.formValidation(notaryForm[0], {
      fields: {
        full_name: {
          validators: {
            notEmpty: {
              message: 'Vui lòng nhập họ và tên'
            },
            stringLength: {
              max: 255,
              message: 'Họ và tên không được vượt quá 255 ký tự'
            }
          }
        },
        position: {
          validators: {
            stringLength: {
              max: 255,
              message: '<PERSON><PERSON><PERSON> v<PERSON> không được vượt quá 255 ký tự'
            }
          }
        },
        certificate_number: {
          validators: {
            notEmpty: {
              message: '<PERSON><PERSON> lòng nhập số chứng chỉ'
            },
            stringLength: {
              max: 100,
              message: '<PERSON><PERSON> chứng chỉ không được vượt quá 100 ký tự'
            }
          }
        },
        certificate_issue_date: {
          validators: {
            date: {
              format: 'YYYY-MM-DD',
              message: 'Ngày cấp chứng chỉ không đúng định dạng'
            }
          }
        },
        certificate_issue_place: {
          validators: {
            stringLength: {
              max: 255,
              message: 'Nơi cấp chứng chỉ không được vượt quá 255 ký tự'
            }
          }
        },
        phone: {
          validators: {
            stringLength: {
              max: 20,
              message: 'Số điện thoại không được vượt quá 20 ký tự'
            },
            regexp: {
              regexp: /^[0-9+\-\s()]*$/,
              message: 'Số điện thoại không đúng định dạng'
            }
          }
        },
        email: {
          validators: {
            emailAddress: {
              message: 'Email không đúng định dạng'
            },
            stringLength: {
              max: 255,
              message: 'Email không được vượt quá 255 ký tự'
            }
          }
        },
        status: {
          validators: {
            notEmpty: {
              message: 'Vui lòng chọn trạng thái'
            }
          }
        }
      },
      plugins: {
        trigger: new FormValidation.plugins.Trigger(),
        bootstrap5: new FormValidation.plugins.Bootstrap5({
          eleValidClass: '',
          rowSelector: function (field, ele) {
            return '.mb-3, .mb-4';
          }
        }),
        submitButton: new FormValidation.plugins.SubmitButton(),
        autoFocus: new FormValidation.plugins.AutoFocus()
      }
    });

    // Handle form submission
    fv.on('core.form.valid', function () {
      // Show loading state
      const submitBtn = notaryForm.find('button[type="submit"]');
      const originalText = submitBtn.html();
      submitBtn.html('<i class="ri-loader-4-line ri-spin me-1"></i>Đang xử lý...').prop('disabled', true);

      // Submit form
      notaryForm[0].submit();
    });
  }

  // Image preview functionality
  const imageInput = $('#image');
  if (imageInput.length) {
    imageInput.on('change', function () {
      const file = this.files[0];
      const preview = $('#imagePreview');
      const previewImg = $('#previewImg');

      if (file) {
        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif'];
        if (!allowedTypes.includes(file.type)) {
          Swal.fire({
            icon: 'error',
            title: 'Lỗi!',
            text: 'Vui lòng chọn file ảnh (JPG, PNG, GIF)'
          });
          $(this).val('');
          if (preview.length) preview.hide();
          return;
        }

        // Validate file size (2MB)
        if (file.size > 2 * 1024 * 1024) {
          Swal.fire({
            icon: 'error',
            title: 'Lỗi!',
            text: 'Kích thước file không được vượt quá 2MB'
          });
          $(this).val('');
          if (preview.length) preview.hide();
          return;
        }

        // Show preview
        const reader = new FileReader();
        reader.onload = function (e) {
          if (previewImg.length) {
            previewImg.attr('src', e.target.result);
          }
          if (preview.length) {
            preview.show();
          }
        };
        reader.readAsDataURL(file);
      } else {
        if (preview.length) preview.hide();
      }
    });
  }

  // Remove image checkbox functionality
  $('#remove_image').on('change', function () {
    if ($(this).is(':checked')) {
      $('.current-image').addClass('opacity-50');
      $('#image').prop('required', false);
    } else {
      $('.current-image').removeClass('opacity-50');
    }
  });

  // Auto-generate certificate number format
  $('#certificate_number').on('input', function () {
    let value = $(this).val().toUpperCase();
    // Remove any non-alphanumeric characters except hyphens
    value = value.replace(/[^A-Z0-9\-]/g, '');
    $(this).val(value);
  });

  // Format phone number
  $('#phone').on('input', function () {
    let value = $(this).val().replace(/\D/g, ''); // Remove non-digits

    // Format as Vietnamese phone number
    if (value.length > 0) {
      if (value.startsWith('84')) {
        // International format
        value = '+84 ' + value.substring(2);
      } else if (value.startsWith('0')) {
        // Domestic format
        if (value.length >= 4) {
          value = value.substring(0, 4) + ' ' + value.substring(4);
        }
        if (value.length >= 9) {
          value = value.substring(0, 9) + ' ' + value.substring(9);
        }
      }
    }

    $(this).val(value);
  });

  // Character counter for notes
  $('#notes').on('input', function () {
    const maxLength = 1000;
    const currentLength = $(this).val().length;
    const remaining = maxLength - currentLength;

    let counterElement = $(this).siblings('.char-counter');
    if (counterElement.length === 0) {
      counterElement = $('<div class="char-counter form-text"></div>');
      $(this).after(counterElement);
    }

    counterElement.text(`${currentLength}/${maxLength} ký tự`);

    if (remaining < 50) {
      counterElement.addClass('text-warning');
    } else {
      counterElement.removeClass('text-warning');
    }

    if (remaining < 0) {
      counterElement.addClass('text-danger').removeClass('text-warning');
    } else {
      counterElement.removeClass('text-danger');
    }
  });

  // Initialize character counter
  $('#notes').trigger('input');

  // Date validation - certificate issue date should not be in the future
  $('#certificate_issue_date').on('change', function () {
    const selectedDate = new Date($(this).val());
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (selectedDate > today) {
      Swal.fire({
        icon: 'warning',
        title: 'Cảnh báo!',
        text: 'Ngày cấp chứng chỉ không được là ngày trong tương lai'
      });
      $(this).val('');
    }
  });

  // Prevent form submission on Enter key (except in textarea)
  notaryForm.on('keypress', function (e) {
    if (e.which === 13 && e.target.tagName !== 'TEXTAREA') {
      e.preventDefault();
      return false;
    }
  });

  // Confirm navigation away from unsaved form
  let formChanged = false;
  notaryForm.on('change input', function () {
    formChanged = true;
  });

  $(window).on('beforeunload', function () {
    if (formChanged) {
      return 'Bạn có thay đổi chưa được lưu. Bạn có chắc chắn muốn rời khỏi trang này?';
    }
  });

  notaryForm.on('submit', function () {
    formChanged = false;
  });

  // Auto-fill functionality when selecting notary office
  $('#notary_office_id').on('change', function () {
    const selectedOfficeId = $(this).val();
    if (selectedOfficeId) {
      // You can add logic here to auto-fill related fields if needed
      console.log('Selected notary office:', selectedOfficeId);
    }
  });
});
