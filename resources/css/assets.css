/**
 * Assets Management Custom Styles
 */

/* Statistics Cards */
.card .avatar-initial {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Form Enhancements */
.form-floating-outline .form-control:focus {
  border-color: var(--bs-primary);
  box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
}

/* Status Badge Enhancements */
.badge.bg-label-success {
  background-color: rgba(var(--bs-success-rgb), 0.1) !important;
  color: var(--bs-success) !important;
}

.badge.bg-label-secondary {
  background-color: rgba(var(--bs-secondary-rgb), 0.1) !important;
  color: var(--bs-secondary) !important;
}

.badge.bg-label-warning {
  background-color: rgba(var(--bs-warning-rgb), 0.1) !important;
  color: var(--bs-warning) !important;
}

.badge.bg-label-info {
  background-color: rgba(var(--bs-info-rgb), 0.1) !important;
  color: var(--bs-info) !important;
}

.badge.bg-label-danger {
  background-color: rgba(var(--bs-danger-rgb), 0.1) !important;
  color: var(--bs-danger) !important;
}

/* DataTable Custom Styles */
.datatables-assets .btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.datatables-assets .btn-sm i {
  font-size: 0.875rem;
}

/* Action Buttons */
.btn-outline-info:hover {
  color: #fff;
  background-color: var(--bs-info);
  border-color: var(--bs-info);
}

.btn-outline-primary:hover {
  color: #fff;
  background-color: var(--bs-primary);
  border-color: var(--bs-primary);
}

.btn-outline-warning:hover {
  color: #fff;
  background-color: var(--bs-warning);
  border-color: var(--bs-warning);
}

.btn-outline-success:hover {
  color: #fff;
  background-color: var(--bs-success);
  border-color: var(--bs-success);
}

.btn-outline-danger:hover {
  color: #fff;
  background-color: var(--bs-danger);
  border-color: var(--bs-danger);
}

/* File Upload Styles */
.file-upload-area {
  border: 2px dashed var(--bs-border-color);
  border-radius: 0.375rem;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.file-upload-area:hover {
  border-color: var(--bs-primary);
  background-color: rgba(var(--bs-primary-rgb), 0.05);
}

.file-upload-area.dragover {
  border-color: var(--bs-success);
  background-color: rgba(var(--bs-success-rgb), 0.1);
}

/* Image Preview Styles */
.img-thumbnail {
  border: 2px solid var(--bs-border-color);
  transition: all 0.3s ease;
  cursor: pointer;
}

.img-thumbnail:hover {
  border-color: var(--bs-primary);
  transform: scale(1.05);
}

/* Asset Detail View */
.asset-detail-card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.asset-detail-card .card-header {
  background-color: var(--bs-light);
  border-bottom: 1px solid var(--bs-border-color);
}

[data-bs-theme="dark"] .asset-detail-card .card-header {
  background-color: var(--bs-dark);
}

/* Filter Section */
.filter-section {
  background-color: var(--bs-light);
  border-radius: 0.375rem;
  padding: 1rem;
  margin-bottom: 1rem;
}

[data-bs-theme="dark"] .filter-section {
  background-color: var(--bs-dark);
}

/* Bulk Actions */
#bulkActions {
  position: sticky;
  top: 1rem;
  z-index: 1020;
}

#bulkActions .card {
  border: 2px solid var(--bs-primary);
  background-color: rgba(var(--bs-primary-rgb), 0.05);
}

/* Area Display */
.area-display {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: var(--bs-success);
}

/* Value Display */
.value-display {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: var(--bs-primary);
}

/* Modal Enhancements */
.modal-xl {
  max-width: 1200px;
}

.modal-body {
  max-height: 70vh;
  overflow-y: auto;
}

/* Form Sections */
.form-section {
  border: 1px solid var(--bs-border-color);
  border-radius: 0.375rem;
  margin-bottom: 1rem;
}

.form-section-header {
  background-color: var(--bs-light);
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--bs-border-color);
  border-radius: 0.375rem 0.375rem 0 0;
}

[data-bs-theme="dark"] .form-section-header {
  background-color: var(--bs-dark);
}

.form-section-body {
  padding: 1rem;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
  .card-header .d-flex {
    flex-direction: column;
    gap: 1rem;
  }
  
  .card-header .btn {
    width: 100%;
  }
  
  .datatables-assets .btn-sm {
    padding: 0.125rem 0.25rem;
    font-size: 0.7rem;
  }
  
  .modal-xl {
    max-width: 95%;
  }
  
  .modal-body {
    max-height: 60vh;
  }
  
  .filter-section .row > div {
    margin-bottom: 0.5rem;
  }
}

/* Dark Mode Adjustments */
[data-bs-theme="dark"] .img-thumbnail {
  border-color: var(--bs-border-color-translucent);
}

[data-bs-theme="dark"] .form-floating-outline .form-control {
  background-color: var(--bs-body-bg);
  border-color: var(--bs-border-color);
}

[data-bs-theme="dark"] .card {
  background-color: var(--bs-card-bg);
  border-color: var(--bs-border-color);
}

[data-bs-theme="dark"] .file-upload-area {
  border-color: var(--bs-border-color-translucent);
}

/* Loading States */
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.spinner-border-sm {
  width: 0.875rem;
  height: 0.875rem;
}

/* Form Validation Enhancements */
.is-invalid {
  border-color: var(--bs-danger) !important;
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: var(--bs-danger);
}

/* Select2 Enhancements */
.select2-container--default .select2-selection--single {
  height: calc(3.5rem + 2px);
  border: 1px solid var(--bs-border-color);
  border-radius: 0.375rem;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  line-height: calc(3.5rem);
  padding-left: 1rem;
  color: var(--bs-body-color);
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  height: calc(3.5rem);
  right: 1rem;
}

/* Asset Code Display */
.asset-code {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: var(--bs-info);
  background-color: rgba(var(--bs-info-rgb), 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
}

/* Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.status-indicator::before {
  content: '';
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background-color: currentColor;
}

/* Quick Stats */
.quick-stat {
  text-align: center;
  padding: 1rem;
  border-radius: 0.375rem;
  background-color: var(--bs-light);
  border: 1px solid var(--bs-border-color);
}

[data-bs-theme="dark"] .quick-stat {
  background-color: var(--bs-dark);
}

.quick-stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--bs-primary);
}

.quick-stat-label {
  font-size: 0.875rem;
  color: var(--bs-secondary);
  margin-top: 0.25rem;
}

/* Print Styles */
@media print {
  .btn, .modal, .card-header .d-flex > div:last-child {
    display: none !important;
  }
  
  .card {
    border: 1px solid #000 !important;
    box-shadow: none !important;
  }
  
  .badge {
    border: 1px solid #000 !important;
  }
}
