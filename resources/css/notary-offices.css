/* Notary Offices Management Styles */

/* Statistics Cards */
.notary-office-stats .card {
  transition: transform 0.2s ease-in-out;
}

.notary-office-stats .card:hover {
  transform: translateY(-2px);
}

/* DataTable Customizations */
.datatables-notary-offices {
  font-size: 0.875rem;
}

.datatables-notary-offices th {
  background-color: var(--bs-gray-100);
  font-weight: 600;
  border-bottom: 2px solid var(--bs-border-color);
}

.datatables-notary-offices td {
  vertical-align: middle;
  padding: 0.75rem 0.5rem;
}

/* Form Styles */
.notary-office-form .card {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.notary-office-form .form-label {
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.notary-office-form .form-control:focus,
.notary-office-form .form-select:focus {
  border-color: var(--bs-primary);
  box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
}

/* Image Upload Styles */
.current-image img {
  border: 2px solid var(--bs-border-color);
  border-radius: 0.375rem;
}

#imagePreview img {
  border: 2px solid var(--bs-success);
  border-radius: 0.375rem;
}

/* Status Badge Styles */
.badge.bg-label-success {
  background-color: rgba(40, 167, 69, 0.1) !important;
  color: #28a745 !important;
}

.badge.bg-label-secondary {
  background-color: rgba(108, 117, 125, 0.1) !important;
  color: #6c757d !important;
}

/* Action Buttons */
.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.btn-outline-info:hover {
  background-color: var(--bs-info);
  border-color: var(--bs-info);
}

.btn-outline-primary:hover {
  background-color: var(--bs-primary);
  border-color: var(--bs-primary);
}

.btn-outline-warning:hover {
  background-color: var(--bs-warning);
  border-color: var(--bs-warning);
}

.btn-outline-success:hover {
  background-color: var(--bs-success);
  border-color: var(--bs-success);
}

.btn-outline-danger:hover {
  background-color: var(--bs-danger);
  border-color: var(--bs-danger);
}

/* Filter Section */
.filter-section {
  background-color: var(--bs-gray-50);
  border-radius: 0.375rem;
  padding: 1rem;
  margin-bottom: 1rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .datatables-notary-offices {
    font-size: 0.75rem;
  }
  
  .btn-sm {
    padding: 0.125rem 0.25rem;
    font-size: 0.7rem;
  }
  
  .card-header h5,
  .card-header h6 {
    font-size: 1rem;
  }
}

/* Loading States */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--bs-primary);
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Custom Scrollbar */
.table-responsive::-webkit-scrollbar {
  height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
  background: var(--bs-gray-100);
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
  background: var(--bs-gray-400);
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
  background: var(--bs-gray-500);
}

/* Form Validation */
.is-invalid {
  border-color: var(--bs-danger) !important;
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: var(--bs-danger);
}

/* Success/Error Messages */
.alert {
  border: none;
  border-radius: 0.5rem;
  padding: 1rem 1.25rem;
}

.alert-success {
  background-color: rgba(40, 167, 69, 0.1);
  color: #155724;
}

.alert-danger {
  background-color: rgba(220, 53, 69, 0.1);
  color: #721c24;
}
