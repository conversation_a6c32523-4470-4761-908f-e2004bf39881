<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('parties', function (Blueprint $table) {
            // Thông tin giấy chứng nhận kết hôn
            $table->string('marriage_certificate_number')->nullable()->after('spouse_address'); // Số giấy chứng nhận kết hôn
            $table->date('marriage_certificate_date')->nullable()->after('marriage_certificate_number'); // <PERSON><PERSON><PERSON> cấp giấy chứng nhận kết hôn
            $table->string('marriage_certificate_place')->nullable()->after('marriage_certificate_date'); // Nơi cấp giấy chứng nhận kết hôn
            $table->date('marriage_date')->nullable()->after('marriage_certificate_place'); // <PERSON><PERSON><PERSON> kết hôn
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('parties', function (Blueprint $table) {
            $table->dropColumn([
                'marriage_certificate_number',
                'marriage_certificate_date',
                'marriage_certificate_place',
                'marriage_date'
            ]);
        });
    }
};
