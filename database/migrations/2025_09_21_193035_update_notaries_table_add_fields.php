<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('notaries', function (Blueprint $table) {
            // Add foreign key to notary_offices
            $table->foreignId('notary_office_id')->nullable()->constrained('notary_offices')->onDelete('set null')->comment('ID phòng công chứng');

            // Personal information
            $table->string('full_name')->comment('Họ và tên');
            $table->string('position')->nullable()->comment('Chức vụ');
            $table->string('certificate_number')->unique()->comment('Số chứng chỉ hành nghề');
            $table->date('certificate_issue_date')->nullable()->comment('<PERSON><PERSON><PERSON> cấp chứng chỉ');
            $table->string('certificate_issue_place')->nullable()->comment('Nơi cấp chứng chỉ');

            // Contact information
            $table->text('address')->nullable()->comment('Địa chỉ');
            $table->string('phone', 20)->nullable()->comment('Số điện thoại');
            $table->string('email')->nullable()->comment('Email');

            // Additional information
            $table->string('image')->nullable()->comment('Ảnh đại diện');
            $table->enum('status', ['active', 'inactive'])->default('active')->comment('Trạng thái');
            $table->text('notes')->nullable()->comment('Ghi chú');

            // Indexes
            $table->index(['notary_office_id']);
            $table->index(['status']);
            $table->index(['full_name']);
            $table->index(['certificate_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('notaries', function (Blueprint $table) {
            $table->dropForeign(['notary_office_id']);
            $table->dropColumn([
                'notary_office_id',
                'full_name',
                'position',
                'certificate_number',
                'certificate_issue_date',
                'certificate_issue_place',
                'address',
                'phone',
                'email',
                'image',
                'status',
                'notes'
            ]);
        });
    }
};
