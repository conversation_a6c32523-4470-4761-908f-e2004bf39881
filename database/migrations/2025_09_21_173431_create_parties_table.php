<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('parties', function (Blueprint $table) {
            $table->id();

            // Thông tin cá nhân cơ bản
            $table->string('full_name'); // Họ và tên
            $table->date('date_of_birth')->nullable(); // Ngày sinh
            $table->enum('gender', ['male', 'female', 'other'])->nullable(); // Giới tính
            $table->string('id_number')->nullable(); // Số CCCD/CMND
            $table->date('id_issue_date')->nullable(); // Ngày cấp CCCD/CMND
            $table->string('id_issue_place')->nullable(); // Nơi cấp CCCD/CMND

            // Địa chỉ liên hệ
            $table->text('permanent_address')->nullable(); // Địa chỉ thường trú
            $table->text('temporary_address')->nullable(); // Địa chỉ tạm trú
            $table->string('phone_number')->nullable(); // Số điện thoại
            $table->string('email')->nullable(); // Email

            // Tình trạng hôn nhân
            $table->enum('marital_status', ['single', 'married', 'divorced', 'widowed'])->default('single'); // Tình trạng hôn nhân
            $table->string('spouse_name')->nullable(); // Tên vợ/chồng (nếu đã kết hôn)
            $table->date('spouse_date_of_birth')->nullable(); // Ngày sinh vợ/chồng
            $table->string('spouse_id_number')->nullable(); // CCCD/CMND vợ/chồng
            $table->text('spouse_address')->nullable(); // Địa chỉ vợ/chồng

            // Nghề nghiệp và nơi làm việc
            $table->string('occupation')->nullable(); // Nghề nghiệp
            $table->string('workplace')->nullable(); // Nơi làm việc
            $table->text('workplace_address')->nullable(); // Địa chỉ nơi làm việc

            // Vai trò trong vụ việc
            $table->enum('role_in_case', ['plaintiff', 'defendant', 'related_party']); // Vai trò: nguyên đơn, bị đơn, người có quyền lợi nghĩa vụ liên quan

            // Ghi chú và trạng thái
            $table->text('notes')->nullable(); // Ghi chú bổ sung
            $table->enum('status', ['active', 'inactive'])->default('active'); // Trạng thái

            $table->timestamps();

            // Indexes
            $table->index(['full_name']);
            $table->index(['id_number']);
            $table->index(['role_in_case']);
            $table->index(['status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('parties');
    }
};
