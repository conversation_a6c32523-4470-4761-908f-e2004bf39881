<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notary_offices', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('Tên phòng công chứng');
            $table->string('code')->unique()->comment('Mã số phòng công chứng');
            $table->text('address')->comment('Địa chỉ');
            $table->string('phone', 20)->nullable()->comment('Số điện thoại');
            $table->string('email')->nullable()->comment('Email');
            $table->string('website')->nullable()->comment('Website');
            $table->text('description')->nullable()->comment('<PERSON><PERSON> tả');
            $table->string('image')->nullable()->comment('Ảnh đại diện');
            $table->enum('status', ['active', 'inactive'])->default('active')->comment('Trạng thái');
            $table->integer('sort_order')->default(0)->comment('Thứ tự sắp xếp');
            $table->timestamps();

            // Indexes
            $table->index(['status']);
            $table->index(['sort_order']);
            $table->index(['name']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notary_offices');
    }
};
