<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assets', function (Blueprint $table) {
            $table->id();

            // Basic asset information
            $table->string('asset_code')->unique()->comment('Mã tài sản');
            $table->string('name')->comment('Tên tài sản');
            $table->text('description')->nullable()->comment('Mô tả tài sản');

            // Land use rights information (based on the document image)
            $table->text('land_origin_source')->nullable()->comment('Nguồn gốc sử dụng đất');
            $table->text('transfer_recognition')->nullable()->comment('Nhận chuyển nhượng đất đượ<PERSON> nh<PERSON>n QSDĐ');
            $table->text('owner_info')->nullable()->comment('Thông tin gia chủ, thửa đất');
            $table->string('map_sheet_number')->nullable()->comment('Tờ bản đồ số');

            // Address information
            $table->string('address')->nullable()->comment('Địa chỉ cụ thể');
            $table->string('ward')->nullable()->comment('Xã/Phường');
            $table->string('district')->nullable()->comment('Quận/Huyện');
            $table->string('province')->nullable()->comment('Tỉnh/Thành phố');

            // Area and usage information
            $table->decimal('total_area', 10, 2)->nullable()->comment('Tổng diện tích (m2)');
            $table->decimal('residential_area', 10, 2)->nullable()->comment('Diện tích đất ở (m2)');
            $table->decimal('agricultural_area', 10, 2)->nullable()->comment('Diện tích đất nông nghiệp (m2)');
            $table->string('usage_form')->nullable()->comment('Hình thức sử dụng');
            $table->text('usage_purpose')->nullable()->comment('Mục đích sử dụng');
            $table->text('usage_term')->nullable()->comment('Thời hạn sử dụng đất');

            // Relationships
            $table->foreignId('asset_type_id')->nullable()->constrained('asset_types')->onDelete('set null')->comment('Loại tài sản');

            // Status and metadata
            $table->enum('status', ['active', 'inactive', 'sold', 'transferred'])->default('active')->comment('Trạng thái');
            $table->decimal('estimated_value', 15, 2)->nullable()->comment('Giá trị ước tính (VND)');
            $table->date('acquisition_date')->nullable()->comment('Ngày tiếp nhận');
            $table->text('notes')->nullable()->comment('Ghi chú');

            // File attachments
            $table->json('documents')->nullable()->comment('Tài liệu đính kèm (JSON)');
            $table->json('images')->nullable()->comment('Hình ảnh đính kèm (JSON)');

            $table->timestamps();

            // Indexes for better performance
            $table->index(['status', 'asset_type_id']);
            $table->index(['province', 'district', 'ward']);
            $table->index('acquisition_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assets');
    }
};
