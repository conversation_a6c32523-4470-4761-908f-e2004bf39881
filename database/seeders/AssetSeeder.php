<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Asset;
use App\Models\AssetType;

class AssetSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get asset types for relationships
        $assetTypes = AssetType::all();

        if ($assetTypes->isEmpty()) {
            $this->command->warn('No asset types found. Please run AssetTypeSeeder first.');
            return;
        }

        $assets = [
            [
                'name' => 'Đất ở tại xã Phú An',
                'description' => 'Thửa đất ở tại xã <PERSON>, thành phố Bến Cát, tỉnh Bình Dương',
                'land_origin_source' => 'Nhà nước giao đất có thu tiền sử dụng đất',
                'transfer_recognition' => 'Nhận chuyển nhượng đất được Công nhận QSDĐ theo Gi<PERSON>y chứng nhận quyền sử dụng đất số 2601',
                'owner_info' => 'Gia chủ: Nguyễn Văn A. Thửa đất có 25m2 đất CLN được Chủ tịch ủy ban nhân dân thành phố Bến Cát, tỉnh Bình Dương duyệt ngày 11/6/2025',
                'map_sheet_number' => '14',
                'address' => 'Thửa đất số 2601',
                'ward' => 'xã Phú An',
                'district' => 'thành phố Bến Cát',
                'province' => 'tỉnh Bình Dương',
                'total_area' => 302.00,
                'residential_area' => 100.00,
                'agricultural_area' => 202.00,
                'usage_form' => 'Sử dụng riêng',
                'usage_purpose' => 'Đất ở tại nông thôn: 100m2; Đất trồng cây lâu năm: 202m2',
                'usage_term' => 'Đất ở tại nông thôn: Lâu dài; Đất trồng cây lâu năm: đến tháng 10/2043',
                'asset_type_id' => $assetTypes->first()->id,
                'status' => 'active',
                'estimated_value' => 1500000000, // 1.5 tỷ VND
                'acquisition_date' => '2025-01-15',
                'notes' => 'Tài sản được tiếp nhận từ đương sự theo hợp đồng công chứng'
            ],
            [
                'name' => 'Nhà ở và đất ở tại TP.HCM',
                'description' => 'Căn nhà 2 tầng và đất ở tại quận 7, TP.HCM',
                'land_origin_source' => 'Mua bán theo quy định của pháp luật',
                'owner_info' => 'Gia chủ: Trần Thị B',
                'map_sheet_number' => '25',
                'address' => '123 Đường Nguyễn Thị Thập',
                'ward' => 'Phường Tân Phú',
                'district' => 'Quận 7',
                'province' => 'TP.Hồ Chí Minh',
                'total_area' => 80.00,
                'residential_area' => 80.00,
                'usage_form' => 'Sử dụng riêng',
                'usage_purpose' => 'Đất ở tại đô thị: 80m2',
                'usage_term' => 'Lâu dài',
                'asset_type_id' => $assetTypes->first()->id,
                'status' => 'active',
                'estimated_value' => 8000000000, // 8 tỷ VND
                'acquisition_date' => '2025-02-01',
                'notes' => 'Nhà xây dựng năm 2020, tình trạng tốt'
            ],
            [
                'name' => 'Đất nông nghiệp tại Đồng Tháp',
                'description' => 'Thửa đất trồng lúa tại huyện Cao Lãnh, tỉnh Đồng Tháp',
                'land_origin_source' => 'Nhà nước giao đất không thu tiền sử dụng đất',
                'owner_info' => 'Gia chủ: Lê Văn C',
                'map_sheet_number' => '08',
                'address' => 'Ấp 3',
                'ward' => 'Xã Mỹ Hiệp',
                'district' => 'Huyện Cao Lãnh',
                'province' => 'Tỉnh Đồng Tháp',
                'total_area' => 5000.00,
                'agricultural_area' => 5000.00,
                'usage_form' => 'Sử dụng riêng',
                'usage_purpose' => 'Đất trồng lúa nước: 5000m2',
                'usage_term' => 'Lâu dài',
                'asset_type_id' => $assetTypes->first()->id,
                'status' => 'active',
                'estimated_value' => 500000000, // 500 triệu VND
                'acquisition_date' => '2025-01-20',
                'notes' => 'Đất màu mỡ, thuận lợi cho việc trồng lúa'
            ],
            [
                'name' => 'Căn hộ chung cư tại Hà Nội',
                'description' => 'Căn hộ 3 phòng ngủ tại chung cư cao cấp',
                'owner_info' => 'Gia chủ: Phạm Văn D',
                'address' => 'Tầng 15, Tòa A, Chung cư Golden Palace',
                'ward' => 'Phường Mễ Trì',
                'district' => 'Quận Nam Từ Liêm',
                'province' => 'TP.Hà Nội',
                'total_area' => 95.00,
                'residential_area' => 95.00,
                'usage_form' => 'Sử dụng riêng',
                'usage_purpose' => 'Nhà ở chung cư: 95m2',
                'usage_term' => 'Theo thời hạn của dự án',
                'asset_type_id' => $assetTypes->first()->id,
                'status' => 'sold',
                'estimated_value' => 4500000000, // 4.5 tỷ VND
                'acquisition_date' => '2024-12-15',
                'notes' => 'Đã bán cho khách hàng vào tháng 3/2025'
            ],
            [
                'name' => 'Đất thương mại tại Đà Nẵng',
                'description' => 'Lô đất mặt tiền đường lớn, phù hợp kinh doanh',
                'land_origin_source' => 'Đấu giá quyền sử dụng đất',
                'owner_info' => 'Gia chủ: Công ty TNHH ABC',
                'map_sheet_number' => '12',
                'address' => '456 Đường Lê Duẩn',
                'ward' => 'Phường Hải Châu I',
                'district' => 'Quận Hải Châu',
                'province' => 'TP.Đà Nẵng',
                'total_area' => 200.00,
                'usage_form' => 'Sử dụng riêng',
                'usage_purpose' => 'Đất thương mại dịch vụ: 200m2',
                'usage_term' => '50 năm kể từ ngày cấp',
                'asset_type_id' => $assetTypes->first()->id,
                'status' => 'inactive',
                'estimated_value' => 12000000000, // 12 tỷ VND
                'acquisition_date' => '2025-01-10',
                'notes' => 'Tạm thời không sử dụng, đang chờ quy hoạch mới'
            ]
        ];

        foreach ($assets as $assetData) {
            Asset::create($assetData);
        }

        $this->command->info('Asset seeder completed successfully!');
        $this->command->info('Created ' . count($assets) . ' sample assets.');
    }
}
