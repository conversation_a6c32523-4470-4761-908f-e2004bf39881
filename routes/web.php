<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\language\LanguageController;
use App\Http\Controllers\pages\HomePage;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\RoleController;
use App\Http\Controllers\Admin\AssetTypeController;
use App\Http\Controllers\Admin\AssetController;
use App\Http\Controllers\Admin\PartyController;

// Main Page Route
Route::middleware(['auth'])->group(function () {
  // locale
  Route::get('/lang/{locale}', [LanguageController::class, 'swap']);
  // home
  Route::get('/', [HomePage::class, 'index'])->name('pages-home');

  // Admin Routes
  Route::prefix('admin')->name('admin.')->group(function () {
    // User Management
    Route::resource('users', UserController::class);
    Route::get('users-data', [UserController::class, 'getData'])->name('users.data');
    Route::post('users/{user}/reset-password', [UserController::class, 'resetPassword'])->name('users.reset-password');

    // Role Management
    Route::resource('roles', RoleController::class);
    Route::get('roles-data', [RoleController::class, 'getData'])->name('roles.data');

    // Asset Type Management
    Route::resource('asset-types', AssetTypeController::class);
    Route::get('asset-types-data', [AssetTypeController::class, 'getData'])->name('asset-types.data');
    Route::post('asset-types/{assetType}/toggle-status', [AssetTypeController::class, 'toggleStatus'])->name('asset-types.toggle-status');
    Route::post('asset-types/update-sort-order', [AssetTypeController::class, 'updateSortOrder'])->name('asset-types.update-sort-order');
    Route::get('api/asset-types', [AssetTypeController::class, 'getAssetTypes'])->name('api.asset-types');

    // Asset Management
    Route::resource('assets', AssetController::class);
    Route::get('assets-data', [AssetController::class, 'getData'])->name('assets.data');
    Route::post('assets/bulk-delete', [AssetController::class, 'bulkDelete'])->name('assets.bulk-delete');
    Route::get('assets/export', [AssetController::class, 'export'])->name('assets.export');

    // Party Management
    Route::resource('parties', PartyController::class);
    Route::get('parties-data', [PartyController::class, 'getData'])->name('parties.data');
    Route::post('parties/{party}/toggle-status', [PartyController::class, 'toggleStatus'])->name('parties.toggle-status');
    Route::get('parties/{party}/quick-view', [PartyController::class, 'quickView'])->name('parties.quick-view');
  });
});
